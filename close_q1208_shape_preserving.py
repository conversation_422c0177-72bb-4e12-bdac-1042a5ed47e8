#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
q_1208.ts 形态保持闭合脚本 v2.0
真正保持原始形态，只进行最小必要的修改
"""

import numpy as np
import os
import sys
import time
from datetime import datetime

try:
    import pyvista as pv
    print("✓ PyVista 可用")
except ImportError:
    print("✗ PyVista 不可用，请安装: pip install pyvista")
    sys.exit(1)

def read_ts_file(filename):
    """读取TS文件"""
    print(f"正在读取文件: {filename}")
    
    vertices = []
    faces = []
    vertex_map = {}
    current_idx = 0
    
    encodings = ['utf-8', 'gbk', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            with open(filename, 'r', encoding=encoding) as file:
                lines = file.readlines()
            
            for i, line in enumerate(lines):
                line = line.strip()
                
                if line.startswith('VRTX'):
                    parts = line.split()
                    if len(parts) >= 5:
                        try:
                            vrtx_id = int(parts[1])
                            x, y, z = float(parts[2]), float(parts[3]), float(parts[4])
                            vertices.append([x, y, z])
                            vertex_map[vrtx_id] = current_idx
                            current_idx += 1
                        except (ValueError, IndexError):
                            continue
                
                elif line.startswith('TRGL'):
                    parts = line.split()
                    if len(parts) >= 4:
                        try:
                            v1, v2, v3 = int(parts[1]), int(parts[2]), int(parts[3])
                            if v1 in vertex_map and v2 in vertex_map and v3 in vertex_map:
                                faces.append([vertex_map[v1], vertex_map[v2], vertex_map[v3]])
                        except (ValueError, IndexError):
                            continue
                
                if (i + 1) % 20000 == 0:
                    print(f"已处理 {i + 1} 行...")
            
            vertices = np.array(vertices, dtype=np.float64)
            faces = np.array(faces, dtype=np.int32)
            
            print(f"✓ 成功读取: {len(vertices)} 个顶点, {len(faces)} 个面")
            return vertices, faces
            
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"读取失败 ({encoding}): {e}")
            continue
    
    return np.array([]), np.array([])

def check_closure(mesh):
    """检查网格闭合性"""
    try:
        boundaries = mesh.extract_feature_edges(boundary_edges=True, 
                                               non_manifold_edges=False, 
                                               manifold_edges=False)
        return boundaries.n_cells
    except:
        return -1

def minimal_clean_only(vertices, faces):
    """最小清理 - 只去除明显的重复和无效面"""
    print("\n=== 最小清理（保持形态）===")
    
    # 创建PyVista网格
    faces_with_header = []
    for face in faces:
        faces_with_header.extend([3, face[0], face[1], face[2]])
    
    mesh = pv.PolyData(vertices, faces_with_header)
    print(f"原始网格: {mesh.n_points} 顶点, {mesh.n_cells} 面")
    
    # 只进行非常轻微的清理
    cleaned_mesh = mesh.clean(tolerance=1e-6)  # 更小的容差
    
    print(f"清理后: {cleaned_mesh.n_points} 顶点, {cleaned_mesh.n_cells} 面")
    
    # 计算变化率
    vertex_change = abs(cleaned_mesh.n_points - mesh.n_points) / mesh.n_points * 100
    face_change = abs(cleaned_mesh.n_cells - mesh.n_cells) / mesh.n_cells * 100
    
    print(f"变化率: 顶点 {vertex_change:.2f}%, 面 {face_change:.2f}%")
    
    return cleaned_mesh

def shape_preserving_closure(mesh, max_vertex_change=10, max_face_change=20):
    """形态保持闭合 - 严格控制几何变化"""
    print("\n=== 形态保持闭合 ===")
    
    original_vertices = mesh.n_points
    original_faces = mesh.n_cells
    
    # 检查初始边界
    initial_boundaries = check_closure(mesh)
    print(f"初始边界边数: {initial_boundaries}")
    
    if initial_boundaries == 0:
        print("✓ 网格已经闭合")
        return mesh, True
    
    # 非常保守的孔洞填充尝试
    hole_sizes = [1, 2, 3, 5]  # 非常小的孔洞大小
    best_mesh = mesh
    best_boundaries = initial_boundaries
    
    for hole_size in hole_sizes:
        try:
            print(f"\n尝试填充孔洞大小: {hole_size}")
            filled_mesh = mesh.fill_holes(hole_size=hole_size)
            
            # 检查几何变化
            vertex_change = abs(filled_mesh.n_points - original_vertices) / original_vertices * 100
            face_change = abs(filled_mesh.n_cells - original_faces) / original_faces * 100
            
            print(f"  几何变化: 顶点 {vertex_change:.2f}%, 面 {face_change:.2f}%")
            
            # 检查边界改善
            boundaries = check_closure(filled_mesh)
            print(f"  边界边数: {boundaries}")
            
            # 如果几何变化在可接受范围内且有改善
            if vertex_change <= max_vertex_change and face_change <= max_face_change:
                if boundaries < best_boundaries:
                    best_mesh = filled_mesh
                    best_boundaries = boundaries
                    print(f"  ✓ 接受此结果（改善了 {initial_boundaries - boundaries} 条边界边）")
                else:
                    print(f"  - 无改善，跳过")
            else:
                print(f"  ✗ 几何变化过大，拒绝（阈值: 顶点{max_vertex_change}%, 面{max_face_change}%）")
                break  # 停止尝试更大的孔洞
                
        except Exception as e:
            print(f"  填充失败: {e}")
            continue
    
    # 报告最终结果
    final_vertex_change = abs(best_mesh.n_points - original_vertices) / original_vertices * 100
    final_face_change = abs(best_mesh.n_cells - original_faces) / original_faces * 100
    boundary_improvement = initial_boundaries - best_boundaries
    
    print(f"\n最终结果:")
    print(f"  顶点: {original_vertices} → {best_mesh.n_points} (变化 {final_vertex_change:.2f}%)")
    print(f"  面数: {original_faces} → {best_mesh.n_cells} (变化 {final_face_change:.2f}%)")
    print(f"  边界边: {initial_boundaries} → {best_boundaries} (改善 {boundary_improvement} 条)")
    
    is_acceptable = (final_vertex_change <= max_vertex_change and 
                    final_face_change <= max_face_change and 
                    boundary_improvement > 0)
    
    if is_acceptable:
        print("✓ 形态保持闭合成功")
    else:
        print("⚠ 无法在保持形态的前提下显著改善闭合性")
    
    return best_mesh, is_acceptable

def extract_vertices_faces(mesh):
    """从PyVista网格提取顶点和面"""
    vertices = mesh.points
    faces = []
    
    for i in range(mesh.n_cells):
        cell = mesh.get_cell(i)
        if cell.n_points == 3:
            faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])
    
    return vertices, np.array(faces)

def write_ts_file(filename, vertices, faces, name=None):
    """写入TS文件"""
    if name is None:
        name = os.path.splitext(os.path.basename(filename))[0]
    
    print(f"正在写入TS文件: {filename}")
    
    try:
        with open(filename, "w", encoding='utf-8') as file:
            file.write("GOCAD TSurf 1 \n")
            file.write("HEADER {\n")
            file.write(f"name: {name}\n")
            file.write("*solid*color: #f0e68c\n")
            file.write("use_feature_color: false\n")
            file.write("}\n")
            file.write("GOCAD_ORIGINAL_COORDINATE_SYSTEM\n")
            file.write("NAME \"SKUA Local\"\n")
            file.write("PROJECTION Unknown\n")
            file.write("DATUM Unknown\n")
            file.write("AXIS_NAME X Y Z\n")
            file.write("AXIS_UNIT m m m\n")
            file.write("ZPOSITIVE Elevation\n")
            file.write("END_ORIGINAL_COORDINATE_SYSTEM\n")
            file.write("TFACE\n")
            
            # 写入顶点
            for i, v in enumerate(vertices):
                file.write(f"VRTX {i+1} {v[0]:.9f} {v[1]:.9f} {v[2]:.9f} \n")
            
            # 写入面
            for face in faces:
                file.write(f"TRGL {face[0]+1} {face[1]+1} {face[2]+1}\n")
            
            file.write("END\n")
        
        print(f"✓ 成功写入TS文件: {len(vertices)} 顶点, {len(faces)} 面")
        return True
        
    except Exception as e:
        print(f"✗ 写入TS文件失败: {e}")
        return False

def main():
    """主处理函数"""
    print("=" * 60)
    print("q_1208.ts 形态保持闭合脚本 v2.0")
    print("真正保持原始形态，只进行最小必要的修改")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查输入文件
    input_file = "q_1208.ts"
    if not os.path.exists(input_file):
        print(f"✗ 输入文件 {input_file} 不存在")
        return False
    
    start_time = time.time()
    
    # 步骤1: 读取文件
    print(f"\n步骤1: 读取TS文件...")
    vertices, faces = read_ts_file(input_file)
    
    if len(vertices) == 0 or len(faces) == 0:
        print("✗ 无法读取输入文件")
        return False
    
    # 步骤2: 最小清理
    print(f"\n步骤2: 最小清理...")
    cleaned_mesh = minimal_clean_only(vertices, faces)
    
    # 步骤3: 形态保持闭合
    print(f"\n步骤3: 形态保持闭合...")
    final_mesh, success = shape_preserving_closure(cleaned_mesh, 
                                                  max_vertex_change=10, 
                                                  max_face_change=20)
    
    # 步骤4: 保存结果
    print(f"\n步骤4: 保存结果...")
    final_vertices, final_faces = extract_vertices_faces(final_mesh)
    
    output_file = "q_1208_shape_preserved.ts"
    write_success = write_ts_file(output_file, final_vertices, final_faces, "q_1208_shape_preserved")
    
    if not write_success:
        return False
    
    # 统计信息
    end_time = time.time()
    print(f"\n" + "=" * 60)
    print("处理完成:")
    print(f"原始: {len(vertices)} 顶点, {len(faces)} 面")
    print(f"最终: {len(final_vertices)} 顶点, {len(final_faces)} 面")
    
    vertex_change = abs(len(final_vertices) - len(vertices)) / len(vertices) * 100
    face_change = abs(len(final_faces) - len(faces)) / len(faces) * 100
    
    print(f"变化: 顶点 {vertex_change:.2f}%, 面 {face_change:.2f}%")
    print(f"处理时间: {end_time - start_time:.2f} 秒")
    print(f"输出文件: {output_file}")
    
    if success:
        print("✓ 形态保持闭合成功!")
    else:
        print("⚠ 部分成功，形态基本保持但闭合改善有限")
    
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✓ 形态保持闭合脚本执行成功!")
            sys.exit(0)
        else:
            print("\n✗ 形态保持闭合脚本执行失败!")
            sys.exit(1)
    except Exception as e:
        print(f"\n未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
