#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
q_1208.ts 保守网格修复脚本
只修复明显的数据错误，不改变网格拓扑结构
避免创造新的空隙
"""

import numpy as np
import os
import sys
import time
from datetime import datetime

def read_ts_file(filename):
    """读取TS文件，保持原始结构"""
    print(f"正在读取文件: {filename}")
    
    vertices = []
    faces = []
    vertex_map = {}
    current_idx = 0
    
    encodings = ['utf-8', 'gbk', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            with open(filename, 'r', encoding=encoding) as file:
                lines = file.readlines()
            
            for i, line in enumerate(lines):
                line = line.strip()
                
                if line.startswith('VRTX'):
                    parts = line.split()
                    if len(parts) >= 5:
                        try:
                            vrtx_id = int(parts[1])
                            x, y, z = float(parts[2]), float(parts[3]), float(parts[4])
                            vertices.append([x, y, z])
                            vertex_map[vrtx_id] = current_idx
                            current_idx += 1
                        except (ValueError, IndexError):
                            continue
                
                elif line.startswith('TRGL'):
                    parts = line.split()
                    if len(parts) >= 4:
                        try:
                            v1, v2, v3 = int(parts[1]), int(parts[2]), int(parts[3])
                            if v1 in vertex_map and v2 in vertex_map and v3 in vertex_map:
                                faces.append([vertex_map[v1], vertex_map[v2], vertex_map[v3]])
                        except (ValueError, IndexError):
                            continue
                
                if (i + 1) % 20000 == 0:
                    print(f"已处理 {i + 1} 行...")
            
            vertices = np.array(vertices, dtype=np.float64)
            faces = np.array(faces, dtype=np.int32)
            
            print(f"✓ 成功读取: {len(vertices)} 个顶点, {len(faces)} 个面")
            return vertices, faces
            
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"读取失败 ({encoding}): {e}")
            continue
    
    return np.array([]), np.array([])

def conservative_repair(vertices, faces):
    """保守的网格修复 - 只修复明显错误，不改变拓扑"""
    print("\n=== 保守网格修复 ===")
    print("只修复明显的数据错误，保持原始拓扑结构")
    
    original_vertex_count = len(vertices)
    original_face_count = len(faces)
    
    print(f"原始: {original_vertex_count} 顶点, {original_face_count} 面")
    
    # 步骤1: 手动去除重复顶点（使用很小的容差）
    print("\n步骤1: 去除重复顶点（保守方式）")
    tolerance = 1e-8  # 非常小的容差，只去除完全相同的点
    
    unique_vertices = []
    vertex_mapping = {}
    
    for i, vertex in enumerate(vertices):
        found_duplicate = False
        for j, unique_vertex in enumerate(unique_vertices):
            if np.allclose(vertex, unique_vertex, atol=tolerance):
                vertex_mapping[i] = j
                found_duplicate = True
                break
        
        if not found_duplicate:
            vertex_mapping[i] = len(unique_vertices)
            unique_vertices.append(vertex)
    
    unique_vertices = np.array(unique_vertices)
    print(f"顶点去重: {len(vertices)} → {len(unique_vertices)} (去除 {len(vertices) - len(unique_vertices)} 个重复)")
    
    # 步骤2: 更新面索引并去除无效面
    print("\n步骤2: 更新面索引并去除无效面")
    
    valid_faces = []
    removed_degenerate = 0
    removed_invalid_area = 0
    
    for face in faces:
        try:
            # 更新顶点索引
            new_face = [vertex_mapping[face[0]], vertex_mapping[face[1]], vertex_mapping[face[2]]]
            
            # 检查是否是退化三角形（三个顶点相同）
            if len(set(new_face)) != 3:
                removed_degenerate += 1
                continue
            
            # 检查面积是否过小
            v0, v1, v2 = unique_vertices[new_face]
            edge1 = v1 - v0
            edge2 = v2 - v0
            cross_product = np.cross(edge1, edge2)
            area = 0.5 * np.linalg.norm(cross_product)
            
            if area < 1e-12:  # 面积极小的面
                removed_invalid_area += 1
                continue
            
            valid_faces.append(new_face)
            
        except (KeyError, IndexError):
            continue
    
    valid_faces = np.array(valid_faces)
    
    print(f"面处理结果:")
    print(f"  原始面数: {len(faces)}")
    print(f"  去除退化面: {removed_degenerate}")
    print(f"  去除无效面积面: {removed_invalid_area}")
    print(f"  保留有效面: {len(valid_faces)}")
    
    # 步骤3: 检查修复效果
    print(f"\n修复总结:")
    vertex_change = abs(len(unique_vertices) - original_vertex_count) / original_vertex_count * 100
    face_change = abs(len(valid_faces) - original_face_count) / original_face_count * 100
    
    print(f"  顶点变化: {vertex_change:.2f}%")
    print(f"  面数变化: {face_change:.2f}%")
    
    if vertex_change < 5 and face_change < 5:
        print("✓ 保守修复成功，变化很小")
    elif vertex_change < 20 and face_change < 20:
        print("✓ 修复完成，变化适中")
    else:
        print("⚠ 修复导致较大变化，请检查结果")
    
    return unique_vertices, valid_faces

def write_ts_file(filename, vertices, faces, name=None):
    """写入TS文件"""
    if name is None:
        name = os.path.splitext(os.path.basename(filename))[0]
    
    print(f"正在写入TS文件: {filename}")
    
    try:
        with open(filename, "w", encoding='utf-8') as file:
            file.write("GOCAD TSurf 1 \n")
            file.write("HEADER {\n")
            file.write(f"name: {name}\n")
            file.write("*solid*color: #f0e68c\n")
            file.write("use_feature_color: false\n")
            file.write("}\n")
            file.write("GOCAD_ORIGINAL_COORDINATE_SYSTEM\n")
            file.write("NAME \"SKUA Local\"\n")
            file.write("PROJECTION Unknown\n")
            file.write("DATUM Unknown\n")
            file.write("AXIS_NAME X Y Z\n")
            file.write("AXIS_UNIT m m m\n")
            file.write("ZPOSITIVE Elevation\n")
            file.write("END_ORIGINAL_COORDINATE_SYSTEM\n")
            file.write("TFACE\n")
            
            # 写入顶点
            for i, v in enumerate(vertices):
                file.write(f"VRTX {i+1} {v[0]:.9f} {v[1]:.9f} {v[2]:.9f} \n")
            
            # 写入面
            for face in faces:
                file.write(f"TRGL {face[0]+1} {face[1]+1} {face[2]+1}\n")
            
            file.write("END\n")
        
        print(f"✓ 成功写入TS文件: {len(vertices)} 顶点, {len(faces)} 面")
        return True
        
    except Exception as e:
        print(f"✗ 写入TS文件失败: {e}")
        return False

def main():
    """主处理函数"""
    print("=" * 60)
    print("q_1208.ts 保守网格修复脚本")
    print("只修复明显的数据错误，不改变网格拓扑结构")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查输入文件
    input_file = "q_1208.ts"
    if not os.path.exists(input_file):
        print(f"✗ 输入文件 {input_file} 不存在")
        return False
    
    start_time = time.time()
    
    # 步骤1: 读取文件
    print(f"\n步骤1: 读取TS文件...")
    vertices, faces = read_ts_file(input_file)
    
    if len(vertices) == 0 or len(faces) == 0:
        print("✗ 无法读取输入文件")
        return False
    
    # 步骤2: 保守修复
    print(f"\n步骤2: 保守网格修复...")
    repaired_vertices, repaired_faces = conservative_repair(vertices, faces)
    
    # 步骤3: 保存结果
    print(f"\n步骤3: 保存结果...")
    output_file = "q_1208_repaired.ts"
    write_success = write_ts_file(output_file, repaired_vertices, repaired_faces, "q_1208_repaired")
    
    if not write_success:
        return False
    
    # 统计信息
    end_time = time.time()
    print(f"\n" + "=" * 60)
    print("修复完成:")
    print(f"原始: {len(vertices)} 顶点, {len(faces)} 面")
    print(f"修复后: {len(repaired_vertices)} 顶点, {len(repaired_faces)} 面")
    
    vertex_change = abs(len(repaired_vertices) - len(vertices)) / len(vertices) * 100
    face_change = abs(len(repaired_faces) - len(faces)) / len(faces) * 100
    
    print(f"变化: 顶点 {vertex_change:.2f}%, 面 {face_change:.2f}%")
    print(f"处理时间: {end_time - start_time:.2f} 秒")
    print(f"输出文件: {output_file}")
    
    if vertex_change < 5 and face_change < 5:
        print("✓ 保守修复成功！变化很小，应该不会增加空隙")
    else:
        print("⚠ 修复导致一定变化，请检查结果")
    
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✓ 保守网格修复脚本执行成功!")
            print("这个版本应该不会增加空隙，只修复明显的数据错误")
            sys.exit(0)
        else:
            print("\n✗ 保守网格修复脚本执行失败!")
            sys.exit(1)
    except Exception as e:
        print(f"\n未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
