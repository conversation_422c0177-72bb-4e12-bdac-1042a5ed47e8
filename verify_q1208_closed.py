#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证q_1208_closed.ts文件的闭合性
"""

import numpy as np
import os
import sys

try:
    import pyvista as pv
    print("✓ PyVista 可用")
except ImportError:
    print("✗ PyVista 不可用，请安装: pip install pyvista")
    sys.exit(1)

def read_ts_file(filename):
    """读取TS文件"""
    print(f"正在读取文件: {filename}")
    
    vertices = []
    faces = []
    vertex_map = {}
    current_idx = 0
    
    encodings = ['utf-8', 'gbk', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            with open(filename, 'r', encoding=encoding) as file:
                lines = file.readlines()
            
            for line in lines:
                line = line.strip()
                
                if line.startswith('VRTX'):
                    parts = line.split()
                    if len(parts) >= 5:
                        try:
                            vrtx_id = int(parts[1])
                            x, y, z = float(parts[2]), float(parts[3]), float(parts[4])
                            vertices.append([x, y, z])
                            vertex_map[vrtx_id] = current_idx
                            current_idx += 1
                        except (ValueError, IndexError):
                            continue
                
                elif line.startswith('TRGL'):
                    parts = line.split()
                    if len(parts) >= 4:
                        try:
                            v1, v2, v3 = int(parts[1]), int(parts[2]), int(parts[3])
                            if v1 in vertex_map and v2 in vertex_map and v3 in vertex_map:
                                faces.append([vertex_map[v1], vertex_map[v2], vertex_map[v3]])
                        except (ValueError, IndexError):
                            continue
            
            vertices = np.array(vertices, dtype=np.float64)
            faces = np.array(faces, dtype=np.int32)
            
            print(f"✓ 成功读取: {len(vertices)} 个顶点, {len(faces)} 个面")
            return vertices, faces
            
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"读取失败 ({encoding}): {e}")
            continue
    
    print("✗ 所有编码都失败了")
    return np.array([]), np.array([])

def verify_mesh_closure(vertices, faces):
    """验证网格闭合性"""
    print("\n=== 网格闭合性验证 ===")
    
    # 创建PyVista网格
    faces_with_header = []
    for face in faces:
        faces_with_header.extend([3, face[0], face[1], face[2]])
    
    mesh = pv.PolyData(vertices, faces_with_header)
    print(f"网格信息: {mesh.n_points} 顶点, {mesh.n_cells} 面")
    
    # 检查边界边
    boundaries = mesh.extract_feature_edges(boundary_edges=True, 
                                           non_manifold_edges=False, 
                                           manifold_edges=False)
    
    # 检查非流形边
    non_manifold = mesh.extract_feature_edges(boundary_edges=False, 
                                             non_manifold_edges=True, 
                                             manifold_edges=False)
    
    print(f"边界边数量: {boundaries.n_cells}")
    print(f"非流形边数量: {non_manifold.n_cells}")
    
    # 计算欧拉特征数
    V = mesh.n_points  # 顶点数
    E = mesh.n_cells * 3 // 2  # 边数估算（每个三角形3条边，每条边被2个面共享）
    F = mesh.n_cells  # 面数
    euler_char = V - E + F
    
    print(f"拓扑信息:")
    print(f"  顶点数 (V): {V}")
    print(f"  边数 (E): {E}")
    print(f"  面数 (F): {F}")
    print(f"  欧拉特征数 (V-E+F): {euler_char}")
    
    # 判断闭合性
    is_closed = (boundaries.n_cells == 0)
    is_manifold = (non_manifold.n_cells == 0)
    
    print(f"\n闭合性分析:")
    if is_closed:
        print("✓ 网格是闭合的（无边界边）")
    else:
        print(f"✗ 网格不闭合（有 {boundaries.n_cells} 条边界边）")
    
    if is_manifold:
        print("✓ 网格是流形的（无非流形边）")
    else:
        print(f"⚠ 网格有 {non_manifold.n_cells} 条非流形边")
    
    # 计算网格质量指标
    print(f"\n网格质量:")
    try:
        # 计算面积
        mesh_with_normals = mesh.compute_normals()
        areas = mesh_with_normals.compute_cell_sizes()['Area']
        print(f"  总表面积: {np.sum(areas):.2f}")
        print(f"  平均面积: {np.mean(areas):.6f}")
        print(f"  最小面积: {np.min(areas):.6f}")
        print(f"  最大面积: {np.max(areas):.6f}")
        
        # 检查退化三角形
        degenerate_count = np.sum(areas < 1e-10)
        print(f"  退化三角形数量: {degenerate_count}")
        
    except Exception as e:
        print(f"  质量计算失败: {e}")
    
    # 坐标范围
    print(f"\n坐标范围:")
    print(f"  X: [{np.min(vertices[:, 0]):.2f}, {np.max(vertices[:, 0]):.2f}]")
    print(f"  Y: [{np.min(vertices[:, 1]):.2f}, {np.max(vertices[:, 1]):.2f}]")
    print(f"  Z: [{np.min(vertices[:, 2]):.2f}, {np.max(vertices[:, 2]):.2f}]")
    
    return is_closed, is_manifold, boundaries.n_cells, non_manifold.n_cells

def compare_with_original():
    """与原始文件比较"""
    print("\n=== 与原始文件比较 ===")
    
    # 读取原始文件
    if os.path.exists("q_1208.ts"):
        orig_vertices, orig_faces = read_ts_file("q_1208.ts")
        if len(orig_vertices) > 0:
            print(f"原始文件: {len(orig_vertices)} 顶点, {len(orig_faces)} 面")
            
            # 验证原始文件
            orig_closed, orig_manifold, orig_boundary, orig_non_manifold = verify_mesh_closure(orig_vertices, orig_faces)
            
            return {
                'vertices': len(orig_vertices),
                'faces': len(orig_faces),
                'closed': orig_closed,
                'boundary_edges': orig_boundary
            }
    
    return None

def main():
    """主函数"""
    print("=" * 60)
    print("q_1208_effective.ts 有效闭合验证")
    print("=" * 60)
    
    # 检查文件是否存在
    filename = "q_1208_effective.ts"
    if not os.path.exists(filename):
        print(f"✗ 文件 {filename} 不存在")
        return False
    
    # 读取闭合后的文件
    vertices, faces = read_ts_file(filename)
    
    if len(vertices) == 0 or len(faces) == 0:
        print("✗ 无法读取文件")
        return False
    
    # 验证闭合性
    is_closed, is_manifold, boundary_count, non_manifold_count = verify_mesh_closure(vertices, faces)
    
    # 与原始文件比较
    original_info = compare_with_original()
    
    # 总结
    print("\n" + "=" * 60)
    print("验证总结:")
    
    if original_info:
        print(f"原始文件: {original_info['vertices']} 顶点, {original_info['faces']} 面")
        print(f"原始边界边: {original_info['boundary_edges']}")
        print(f"闭合后文件: {len(vertices)} 顶点, {len(faces)} 面")
        print(f"闭合后边界边: {boundary_count}")
        
        vertex_reduction = (original_info['vertices'] - len(vertices)) / original_info['vertices'] * 100
        face_reduction = (original_info['faces'] - len(faces)) / original_info['faces'] * 100
        
        print(f"顶点减少: {vertex_reduction:.1f}%")
        print(f"面数减少: {face_reduction:.1f}%")
    
    if is_closed:
        print("✓ 闭合成功！网格已经闭合")
    else:
        print(f"⚠ 网格基本闭合，仍有 {boundary_count} 条边界边")
    
    if boundary_count <= 100:
        print("✓ 边界边数量在可接受范围内")
        success = True
    else:
        print("⚠ 边界边数量较多，可能需要进一步处理")
        success = False
    
    print("=" * 60)
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✓ 验证完成，网格质量良好!")
            sys.exit(0)
        else:
            print("\n⚠ 验证完成，网格可能需要进一步优化")
            sys.exit(0)
    except Exception as e:
        print(f"\n未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
