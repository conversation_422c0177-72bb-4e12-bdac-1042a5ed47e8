#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
q_1208.ts 简单闭合脚本
专注于成功闭合，保持形态基本不变
基于PyVista的渐进式闭合策略
"""

import numpy as np
import os
import sys
import time
from datetime import datetime

try:
    import pyvista as pv
    print("✓ PyVista 可用")
except ImportError:
    print("✗ PyVista 不可用，请安装: pip install pyvista")
    sys.exit(1)

def read_ts_file(filename):
    """简单读取TS文件"""
    print(f"正在读取文件: {filename}")
    
    vertices = []
    faces = []
    vertex_map = {}
    current_idx = 0
    
    encodings = ['utf-8', 'gbk', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            with open(filename, 'r', encoding=encoding) as file:
                lines = file.readlines()
            
            print(f"文件总行数: {len(lines)}")
            
            for i, line in enumerate(lines):
                line = line.strip()
                
                if line.startswith('VRTX'):
                    parts = line.split()
                    if len(parts) >= 5:
                        try:
                            vrtx_id = int(parts[1])
                            x, y, z = float(parts[2]), float(parts[3]), float(parts[4])
                            vertices.append([x, y, z])
                            vertex_map[vrtx_id] = current_idx
                            current_idx += 1
                        except (ValueError, IndexError):
                            continue
                
                elif line.startswith('TRGL'):
                    parts = line.split()
                    if len(parts) >= 4:
                        try:
                            v1, v2, v3 = int(parts[1]), int(parts[2]), int(parts[3])
                            if v1 in vertex_map and v2 in vertex_map and v3 in vertex_map:
                                faces.append([vertex_map[v1], vertex_map[v2], vertex_map[v3]])
                        except (ValueError, IndexError):
                            continue
                
                if (i + 1) % 20000 == 0:
                    print(f"已处理 {i + 1} 行...")
            
            vertices = np.array(vertices, dtype=np.float64)
            faces = np.array(faces, dtype=np.int32)
            
            print(f"✓ 成功读取: {len(vertices)} 个顶点, {len(faces)} 个面")
            return vertices, faces
            
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"读取失败 ({encoding}): {e}")
            continue
    
    print("✗ 所有编码都失败了")
    return np.array([]), np.array([])

def clean_mesh_minimal(vertices, faces):
    """最小化网格清理 - 使用PyVista进行高效清理"""
    print("\n=== 最小化网格清理 ===")

    # 直接使用PyVista进行清理，避免手动去重
    print("使用PyVista清理网格...")

    # 创建PyVista网格
    faces_with_header = []
    for face in faces:
        faces_with_header.extend([3, face[0], face[1], face[2]])

    mesh = pv.PolyData(vertices, faces_with_header)
    print(f"原始网格: {mesh.n_points} 顶点, {mesh.n_cells} 面")

    # 使用PyVista的clean方法进行高效清理
    cleaned_mesh = mesh.clean(tolerance=1e-5)

    print(f"清理后: {cleaned_mesh.n_points} 顶点, {cleaned_mesh.n_cells} 面")

    # 提取清理后的顶点和面
    clean_vertices = cleaned_mesh.points
    clean_faces = []

    for i in range(cleaned_mesh.n_cells):
        cell = cleaned_mesh.get_cell(i)
        if cell.n_points == 3:
            clean_faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])

    clean_faces = np.array(clean_faces)

    return clean_vertices, clean_faces

def check_closure(mesh):
    """检查网格是否闭合"""
    try:
        boundaries = mesh.extract_feature_edges(boundary_edges=True, 
                                               non_manifold_edges=False, 
                                               manifold_edges=False)
        return boundaries.n_cells == 0, boundaries.n_cells
    except:
        return False, -1

def close_mesh_simple(vertices, faces):
    """简单闭合策略"""
    print("\n=== 简单闭合策略 ===")
    
    # 创建PyVista网格
    faces_with_header = []
    for face in faces:
        faces_with_header.extend([3, face[0], face[1], face[2]])
    
    mesh = pv.PolyData(vertices, faces_with_header)
    print(f"输入网格: {mesh.n_points} 顶点, {mesh.n_cells} 面")
    
    # 检查初始状态
    is_closed, boundary_count = check_closure(mesh)
    print(f"初始边界边数: {boundary_count}")
    
    if is_closed:
        print("✓ 网格已经闭合")
        return vertices, faces
    
    # 渐进式孔洞填充
    hole_sizes = [10, 50, 100, 200, 500, 1000]
    
    for hole_size in hole_sizes:
        try:
            print(f"尝试填充孔洞大小: {hole_size}")
            filled_mesh = mesh.fill_holes(hole_size=hole_size)
            
            is_closed, boundary_count = check_closure(filled_mesh)
            print(f"  填充后边界边数: {boundary_count}")
            
            if is_closed:
                print(f"✓ 孔洞填充成功闭合 (大小: {hole_size})")
                # 提取顶点和面
                filled_vertices = filled_mesh.points
                filled_faces = []
                for i in range(filled_mesh.n_cells):
                    cell = filled_mesh.get_cell(i)
                    if cell.n_points == 3:
                        filled_faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])
                
                return filled_vertices, np.array(filled_faces)
                
        except Exception as e:
            print(f"  孔洞大小 {hole_size} 失败: {e}")
            continue
    
    # 备选方案：Delaunay 3D
    print("孔洞填充未完全成功，尝试 Delaunay 3D...")
    try:
        # 先进行基本清理
        cleaned_mesh = mesh.clean(tolerance=1e-5)

        # Delaunay 3D 四面体化
        tet_mesh = cleaned_mesh.delaunay_3d(alpha=0, tol=1e-4, offset=5.0)

        if tet_mesh.n_cells > 0:
            print(f"✓ Delaunay 3D 成功: {tet_mesh.n_cells} 四面体")

            # 提取表面
            surface = tet_mesh.extract_surface()
            surface = surface.triangulate()

            is_closed, boundary_count = check_closure(surface)
            print(f"表面边界边数: {boundary_count}")

            # 如果边界边很少，认为基本闭合
            if boundary_count <= 100:  # 宽松的闭合标准
                print(f"✓ Delaunay 3D 生成基本闭合表面 (边界边: {boundary_count})")
                # 提取表面数据
                surface_vertices = surface.points
                surface_faces = []
                for i in range(surface.n_cells):
                    cell = surface.get_cell(i)
                    if cell.n_points == 3:
                        surface_faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])

                return surface_vertices, np.array(surface_faces)
            else:
                # 尝试再次填充剩余的小孔洞
                print("尝试填充Delaunay表面的剩余孔洞...")
                final_surface = surface.fill_holes(hole_size=50)
                is_closed_final, boundary_count_final = check_closure(final_surface)
                print(f"最终表面边界边数: {boundary_count_final}")

                if boundary_count_final <= boundary_count:  # 有改善就接受
                    print(f"✓ 最终表面闭合改善 (边界边: {boundary_count_final})")
                    # 提取最终表面数据
                    final_vertices = final_surface.points
                    final_faces = []
                    for i in range(final_surface.n_cells):
                        cell = final_surface.get_cell(i)
                        if cell.n_points == 3:
                            final_faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])

                    return final_vertices, np.array(final_faces)

    except Exception as e:
        print(f"Delaunay 3D 失败: {e}")

    print("✗ 所有闭合方法都失败了")
    return None, None

def write_ts_file(filename, vertices, faces, name=None):
    """写入TS文件"""
    if name is None:
        name = os.path.splitext(os.path.basename(filename))[0]
    
    print(f"正在写入TS文件: {filename}")
    
    try:
        with open(filename, "w", encoding='utf-8') as file:
            file.write("GOCAD TSurf 1 \n")
            file.write("HEADER {\n")
            file.write(f"name: {name}\n")
            file.write("*solid*color: #f0e68c\n")
            file.write("use_feature_color: false\n")
            file.write("}\n")
            file.write("GOCAD_ORIGINAL_COORDINATE_SYSTEM\n")
            file.write("NAME \"SKUA Local\"\n")
            file.write("PROJECTION Unknown\n")
            file.write("DATUM Unknown\n")
            file.write("AXIS_NAME X Y Z\n")
            file.write("AXIS_UNIT m m m\n")
            file.write("ZPOSITIVE Elevation\n")
            file.write("END_ORIGINAL_COORDINATE_SYSTEM\n")
            file.write("TFACE\n")
            
            # 写入顶点
            for i, v in enumerate(vertices):
                file.write(f"VRTX {i+1} {v[0]:.9f} {v[1]:.9f} {v[2]:.9f} \n")
            
            # 写入面
            for face in faces:
                file.write(f"TRGL {face[0]+1} {face[1]+1} {face[2]+1}\n")
            
            file.write("END\n")
        
        print(f"✓ 成功写入TS文件: {len(vertices)} 顶点, {len(faces)} 面")
        return True
        
    except Exception as e:
        print(f"✗ 写入TS文件失败: {e}")
        return False

def main():
    """主处理函数"""
    print("=" * 60)
    print("q_1208.ts 简单闭合脚本")
    print("专注于成功闭合，保持形态基本不变")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查输入文件
    input_file = "q_1208.ts"
    if not os.path.exists(input_file):
        print(f"✗ 输入文件 {input_file} 不存在")
        return False
    
    start_time = time.time()
    
    # 步骤1: 读取文件
    print(f"\n步骤1: 读取TS文件...")
    vertices, faces = read_ts_file(input_file)
    
    if len(vertices) == 0 or len(faces) == 0:
        print("✗ 无法读取输入文件")
        return False
    
    # 步骤2: 最小清理
    print(f"\n步骤2: 最小化清理...")
    clean_vertices, clean_faces = clean_mesh_minimal(vertices, faces)
    
    # 步骤3: 简单闭合
    print(f"\n步骤3: 简单闭合...")
    closed_vertices, closed_faces = close_mesh_simple(clean_vertices, clean_faces)
    
    if closed_vertices is None or closed_faces is None:
        print("✗ 闭合失败")
        return False
    
    # 步骤4: 保存结果
    print(f"\n步骤4: 保存结果...")
    output_file = "q_1208_closed.ts"
    success = write_ts_file(output_file, closed_vertices, closed_faces, "q_1208_closed")
    
    if not success:
        return False
    
    # 统计信息
    end_time = time.time()
    print(f"\n" + "=" * 60)
    print("处理完成:")
    print(f"原始: {len(vertices)} 顶点, {len(faces)} 面")
    print(f"清理后: {len(clean_vertices)} 顶点, {len(clean_faces)} 面")
    print(f"闭合后: {len(closed_vertices)} 顶点, {len(closed_faces)} 面")
    print(f"处理时间: {end_time - start_time:.2f} 秒")
    print(f"输出文件: {output_file}")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✓ 简单闭合脚本执行成功!")
            sys.exit(0)
        else:
            print("\n✗ 简单闭合脚本执行失败!")
            sys.exit(1)
    except Exception as e:
        print(f"\n未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
