#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细分析 q_1208.ts 的四面体化结果和闭合性
"""

import numpy as np
import os
import sys
import time
from typing import Tuple, List, Optional

def write_tsurf_file(filename: str, vertices: np.ndarray, faces: np.ndar<PERSON>, name: str = "closed_surface"):
    """
    将顶点和面数据写入GOCAD TSurf格式文件

    Args:
        filename: 输出文件名
        vertices: 顶点数组 (N, 3)
        faces: 面数组 (M, 3)
        name: 表面名称
    """
    print(f"正在导出TS文件: {filename}")

    with open(filename, 'w', encoding='utf-8') as f:
        # 写入头部信息
        f.write("GOCAD TSurf 1\n")
        f.write("HEADER {\n")
        f.write(f"name: {name}\n")
        f.write("*solid*color: #00ff00\n")  # 绿色
        f.write("use_feature_color: false\n")
        f.write("}\n")

        # 写入坐标系信息
        f.write("GOCAD_ORIGINAL_COORDINATE_SYSTEM\n")
        f.write('NAME "Local"\n')
        f.write("PROJECTION Unknown\n")
        f.write("DATUM Unknown\n")
        f.write("AXIS_NAME X Y Z\n")
        f.write("AXIS_UNIT m m m\n")
        f.write("ZPOSITIVE Elevation\n")
        f.write("END_ORIGINAL_COORDINATE_SYSTEM\n")

        # 写入属性头部
        f.write("PROPERTY_CLASS_HEADER X {\n")
        f.write("kind: X\n")
        f.write("unit: m\n")
        f.write("pclip: 99\n")
        f.write("}\n")
        f.write("PROPERTY_CLASS_HEADER Y {\n")
        f.write("kind: Y\n")
        f.write("unit: m\n")
        f.write("pclip: 99\n")
        f.write("}\n")
        f.write("PROPERTY_CLASS_HEADER Z {\n")
        f.write("kind: Depth\n")
        f.write("unit: m\n")
        f.write("is_z: on\n")
        f.write("pclip: 99\n")
        f.write("}\n")

        # 开始面定义
        f.write("TFACE\n")

        # 写入顶点
        for i, vertex in enumerate(vertices):
            f.write(f"VRTX {i+1} {vertex[0]:.6f} {vertex[1]:.6f} {vertex[2]:.6f}\n")

        # 写入三角形面
        for face in faces:
            f.write(f"TRGL {face[0]+1} {face[1]+1} {face[2]+1}\n")

        # 结束标记
        f.write("END\n")

    print(f"✓ 成功导出TS文件: {filename}")
    print(f"  顶点数: {len(vertices)}")
    print(f"  面数: {len(faces)}")

def read_tsurf_data(file_path: str) -> Tuple[np.ndarray, np.ndarray]:
    """读取 GOCAD TSurf 格式文件"""
    print(f"正在读取 TS 文件: {file_path}")
    
    encodings = ['utf-8', 'gbk', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            vertices = []
            vertex_map = {}
            faces = []
            current_idx = 0
            
            with open(file_path, 'r', encoding=encoding) as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    
                    if line.startswith('VRTX'):
                        parts = line.split()
                        if len(parts) >= 5:
                            vertex_id = int(parts[1])
                            x, y, z = float(parts[2]), float(parts[3]), float(parts[4])
                            vertices.append([x, y, z])
                            vertex_map[vertex_id] = current_idx
                            current_idx += 1
                    
                    elif line.startswith('TRGL'):
                        parts = line.split()
                        if len(parts) >= 4:
                            v1, v2, v3 = int(parts[1]), int(parts[2]), int(parts[3])
                            if v1 in vertex_map and v2 in vertex_map and v3 in vertex_map:
                                faces.append([vertex_map[v1], vertex_map[v2], vertex_map[v3]])
            
            vertices = np.array(vertices, dtype=np.float64)
            faces = np.array(faces, dtype=np.int32)
            
            print(f"成功读取: {len(vertices)} 个顶点, {len(faces)} 个面")
            return vertices, faces
            
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"使用编码 {encoding} 读取失败: {e}")
            continue
    
    raise ValueError(f"无法读取文件 {file_path}")

def analyze_mesh_topology(vertices: np.ndarray, faces: np.ndarray) -> dict:
    """详细分析网格拓扑结构"""
    print("\n=== 网格拓扑结构分析 ===")
    
    # 统计边的使用次数
    edge_count = {}
    face_edges = {}  # 记录每个面的边
    
    for face_idx, face in enumerate(faces):
        edges = [
            tuple(sorted([face[0], face[1]])),
            tuple(sorted([face[1], face[2]])),
            tuple(sorted([face[2], face[0]]))
        ]
        face_edges[face_idx] = edges
        
        for edge in edges:
            if edge not in edge_count:
                edge_count[edge] = []
            edge_count[edge].append(face_idx)
    
    # 分析边的类型
    boundary_edges = []
    internal_edges = []
    problematic_edges = []
    
    for edge, face_list in edge_count.items():
        count = len(face_list)
        if count == 1:
            boundary_edges.append((edge, face_list))
        elif count == 2:
            internal_edges.append((edge, face_list))
        else:
            problematic_edges.append((edge, face_list))
    
    # 计算欧拉特征数 (V - E + F)
    V = len(vertices)
    E = len(edge_count)
    F = len(faces)
    euler_char = V - E + F
    
    # 分析连通分量
    print("分析连通分量...")
    vertex_neighbors = {i: set() for i in range(len(vertices))}
    
    for face in faces:
        for i in range(3):
            for j in range(3):
                if i != j:
                    vertex_neighbors[face[i]].add(face[j])
    
    # 使用迭代DFS找连通分量
    visited = set()
    components = []

    def iterative_dfs(start_vertex):
        component = []
        stack = [start_vertex]

        while stack:
            vertex = stack.pop()
            if vertex in visited:
                continue
            visited.add(vertex)
            component.append(vertex)

            for neighbor in vertex_neighbors[vertex]:
                if neighbor not in visited:
                    stack.append(neighbor)

        return component

    for vertex in range(len(vertices)):
        if vertex not in visited:
            component = iterative_dfs(vertex)
            if component:
                components.append(component)
    
    result = {
        'vertices': V,
        'edges': E,
        'faces': F,
        'euler_characteristic': euler_char,
        'boundary_edges': len(boundary_edges),
        'internal_edges': len(internal_edges),
        'problematic_edges': len(problematic_edges),
        'connected_components': len(components),
        'largest_component_size': max(len(comp) for comp in components) if components else 0,
        'is_manifold': len(problematic_edges) == 0,
        'is_closed': len(boundary_edges) == 0,
        'boundary_edge_details': boundary_edges[:20],  # 前20个边界边的详细信息
        'problematic_edge_details': problematic_edges[:10]  # 前10个问题边的详细信息
    }
    
    print(f"顶点数: {V}")
    print(f"边数: {E}")
    print(f"面数: {F}")
    print(f"欧拉特征数: {euler_char}")
    print(f"边界边数: {len(boundary_edges)}")
    print(f"内部边数: {len(internal_edges)}")
    print(f"问题边数: {len(problematic_edges)}")
    print(f"连通分量数: {len(components)}")
    print(f"最大连通分量大小: {result['largest_component_size']}")
    print(f"是否为流形: {'是' if result['is_manifold'] else '否'}")
    print(f"是否闭合: {'是' if result['is_closed'] else '否'}")
    
    return result

def test_pyvista_with_closure_check(vertices: np.ndarray, faces: np.ndarray):
    """使用PyVista进行四面体化并检查结果的闭合性"""
    print("\n=== PyVista 四面体化与闭合性检查 ===")
    
    try:
        import pyvista as pv
        print("✓ PyVista 库可用")
    except ImportError as e:
        print(f"✗ PyVista 库不可用: {e}")
        return None
    
    try:
        # 创建PyVista表面网格
        faces_with_header = []
        for face in faces:
            faces_with_header.extend([3, face[0], face[1], face[2]])
        faces_array = np.array(faces_with_header)
        
        surface_mesh = pv.PolyData(vertices, faces_array)
        print(f"原始表面网格: {surface_mesh.n_points} 顶点, {surface_mesh.n_cells} 面")
        
        # 清理网格
        cleaned_mesh = surface_mesh.clean()
        print(f"清理后网格: {cleaned_mesh.n_points} 顶点, {cleaned_mesh.n_cells} 面")
        
        # 尝试填充孔洞
        try:
            filled_mesh = cleaned_mesh.fill_holes(hole_size=1000.0)
            print(f"填充孔洞后: {filled_mesh.n_points} 顶点, {filled_mesh.n_cells} 面")
            surface_mesh = filled_mesh
        except Exception as e:
            print(f"填充孔洞失败: {e}")
            surface_mesh = cleaned_mesh
        
        # 进行四面体化
        print("进行 Delaunay 3D 四面体化...")
        tetrahedral_mesh = surface_mesh.delaunay_3d()
        
        if tetrahedral_mesh.n_cells > 0:
            print(f"✓ 四面体化成功: {tetrahedral_mesh.n_points} 顶点, {tetrahedral_mesh.n_cells} 四面体")
            
            # 提取表面网格
            print("提取四面体网格的表面...")
            extracted_surface = tetrahedral_mesh.extract_surface()
            print(f"提取的表面: {extracted_surface.n_points} 顶点, {extracted_surface.n_cells} 面")
            
            # 检查提取的表面是否闭合
            if extracted_surface.n_cells > 0:
                # 转换为numpy数组进行分析
                surface_faces = []
                for i in range(extracted_surface.n_cells):
                    cell = extracted_surface.get_cell(i)
                    if cell.n_points == 3:
                        surface_faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])
                
                if surface_faces:
                    surface_faces = np.array(surface_faces)
                    surface_vertices = extracted_surface.points
                    
                    print("\n检查提取表面的拓扑结构...")
                    surface_analysis = analyze_mesh_topology(surface_vertices, surface_faces)

                    if surface_analysis['is_closed']:
                        print("✓ 四面体化后的表面是闭合的")

                        # 导出闭合表面为TS文件
                        output_filename = "q_1208_closed_surface.ts"
                        try:
                            write_tsurf_file(output_filename, surface_vertices, surface_faces, "q_1208_closed")
                            print(f"✓ 已导出闭合表面到: {output_filename}")
                        except Exception as e:
                            print(f"✗ 导出TS文件失败: {e}")
                    else:
                        print("✗ 四面体化后的表面仍不闭合")
                        print(f"  剩余边界边数: {surface_analysis['boundary_edges']}")

            return tetrahedral_mesh
        else:
            print("✗ 四面体化结果为空")
            return None
            
    except Exception as e:
        print(f"✗ PyVista 四面体化失败: {e}")
        return None

def main():
    """主函数"""
    print("=" * 70)
    print("q_1208.ts 详细四面体化分析")
    print("=" * 70)
    
    ts_file = "q_1208.ts"
    if not os.path.exists(ts_file):
        print(f"错误: 文件 {ts_file} 不存在")
        return
    
    try:
        # 1. 读取TS文件
        vertices, faces = read_tsurf_data(ts_file)
        
        if len(vertices) == 0 or len(faces) == 0:
            print("错误: 无法从文件中读取有效的网格数据")
            return
        
        # 2. 详细分析输入网格的拓扑结构
        input_analysis = analyze_mesh_topology(vertices, faces)
        
        # 3. 使用PyVista进行四面体化并检查闭合性
        tetrahedral_mesh = test_pyvista_with_closure_check(vertices, faces)
        
        # 4. 总结分析结果
        print("\n" + "=" * 70)
        print("分析结果总结")
        print("=" * 70)
        
        print("输入网格分析:")
        print(f"  - 顶点数: {input_analysis['vertices']}")
        print(f"  - 面数: {input_analysis['faces']}")
        print(f"  - 边数: {input_analysis['edges']}")
        print(f"  - 欧拉特征数: {input_analysis['euler_characteristic']}")
        print(f"  - 连通分量数: {input_analysis['connected_components']}")
        print(f"  - 是否为流形: {'是' if input_analysis['is_manifold'] else '否'}")
        print(f"  - 是否闭合: {'是' if input_analysis['is_closed'] else '否'}")
        
        if not input_analysis['is_closed']:
            print(f"  - 边界边数: {input_analysis['boundary_edges']}")
        
        if tetrahedral_mesh is not None:
            print(f"\n四面体化结果:")
            print(f"  - ✓ 成功生成四面体网格")
            print(f"  - 顶点数: {tetrahedral_mesh.n_points}")
            print(f"  - 四面体数: {tetrahedral_mesh.n_cells}")
        else:
            print(f"\n四面体化结果:")
            print(f"  - ✗ 四面体化失败")
        
        print("\n结论:")
        if input_analysis['is_closed']:
            print("- 输入网格是闭合的，适合进行四面体化")
        else:
            print("- 输入网格不闭合，存在孔洞或边界")
            print("- TetGen的'pY'参数要求输入为闭合流形，因此失败")
            print("- PyVista的Delaunay 3D算法对非闭合网格更宽容")
        
        print("\n测试完成!")
        
    except Exception as e:
        print(f"分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
