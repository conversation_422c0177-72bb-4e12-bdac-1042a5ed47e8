#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
q_1208.ts 平衡闭合脚本
在形态保持和闭合效果之间找到平衡点
"""

import numpy as np
import os
import sys
import time
from datetime import datetime

try:
    import pyvista as pv
    print("✓ PyVista 可用")
except ImportError:
    print("✗ PyVista 不可用，请安装: pip install pyvista")
    sys.exit(1)

def read_ts_file(filename):
    """读取TS文件"""
    print(f"正在读取文件: {filename}")
    
    vertices = []
    faces = []
    vertex_map = {}
    current_idx = 0
    
    encodings = ['utf-8', 'gbk', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            with open(filename, 'r', encoding=encoding) as file:
                lines = file.readlines()
            
            for i, line in enumerate(lines):
                line = line.strip()
                
                if line.startswith('VRTX'):
                    parts = line.split()
                    if len(parts) >= 5:
                        try:
                            vrtx_id = int(parts[1])
                            x, y, z = float(parts[2]), float(parts[3]), float(parts[4])
                            vertices.append([x, y, z])
                            vertex_map[vrtx_id] = current_idx
                            current_idx += 1
                        except (ValueError, IndexError):
                            continue
                
                elif line.startswith('TRGL'):
                    parts = line.split()
                    if len(parts) >= 4:
                        try:
                            v1, v2, v3 = int(parts[1]), int(parts[2]), int(parts[3])
                            if v1 in vertex_map and v2 in vertex_map and v3 in vertex_map:
                                faces.append([vertex_map[v1], vertex_map[v2], vertex_map[v3]])
                        except (ValueError, IndexError):
                            continue
                
                if (i + 1) % 20000 == 0:
                    print(f"已处理 {i + 1} 行...")
            
            vertices = np.array(vertices, dtype=np.float64)
            faces = np.array(faces, dtype=np.int32)
            
            print(f"✓ 成功读取: {len(vertices)} 个顶点, {len(faces)} 个面")
            return vertices, faces
            
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"读取失败 ({encoding}): {e}")
            continue
    
    return np.array([]), np.array([])

def check_closure(mesh):
    """检查网格闭合性"""
    try:
        boundaries = mesh.extract_feature_edges(boundary_edges=True, 
                                               non_manifold_edges=False, 
                                               manifold_edges=False)
        return boundaries.n_cells
    except:
        return -1

def balanced_closure(vertices, faces):
    """平衡的闭合策略"""
    print("\n=== 平衡闭合策略 ===")
    
    # 创建PyVista网格
    faces_with_header = []
    for face in faces:
        faces_with_header.extend([3, face[0], face[1], face[2]])
    
    mesh = pv.PolyData(vertices, faces_with_header)
    print(f"原始网格: {mesh.n_points} 顶点, {mesh.n_cells} 面")
    
    # 轻微清理
    cleaned_mesh = mesh.clean(tolerance=1e-5)
    print(f"清理后: {cleaned_mesh.n_points} 顶点, {cleaned_mesh.n_cells} 面")
    
    # 检查初始边界
    initial_boundaries = check_closure(cleaned_mesh)
    print(f"初始边界边数: {initial_boundaries}")
    
    if initial_boundaries == 0:
        print("✓ 网格已经闭合")
        return cleaned_mesh
    
    # 分阶段填充策略
    current_mesh = cleaned_mesh
    
    # 第一阶段：填充小孔洞
    print("\n第一阶段：填充小孔洞")
    hole_sizes_small = [5, 10, 15, 20]
    
    for hole_size in hole_sizes_small:
        try:
            filled_mesh = current_mesh.fill_holes(hole_size=hole_size)
            boundaries = check_closure(filled_mesh)
            
            vertex_change = abs(filled_mesh.n_points - mesh.n_points) / mesh.n_points * 100
            face_change = abs(filled_mesh.n_cells - mesh.n_cells) / mesh.n_cells * 100
            
            print(f"  孔洞大小 {hole_size}: {boundaries} 边界边, 变化: 顶点{vertex_change:.1f}%, 面{face_change:.1f}%")
            
            # 如果变化不太大，接受结果
            if vertex_change <= 25 and face_change <= 30:
                current_mesh = filled_mesh
            else:
                print(f"    变化过大，停止小孔洞填充")
                break
                
        except Exception as e:
            print(f"  孔洞大小 {hole_size} 失败: {e}")
            break
    
    # 第二阶段：中等孔洞填充
    print("\n第二阶段：中等孔洞填充")
    current_boundaries = check_closure(current_mesh)
    
    if current_boundaries > 1000:  # 如果还有很多边界边
        hole_sizes_medium = [30, 50, 80]
        
        for hole_size in hole_sizes_medium:
            try:
                filled_mesh = current_mesh.fill_holes(hole_size=hole_size)
                boundaries = check_closure(filled_mesh)
                
                vertex_change = abs(filled_mesh.n_points - mesh.n_points) / mesh.n_points * 100
                face_change = abs(filled_mesh.n_cells - mesh.n_cells) / mesh.n_cells * 100
                
                print(f"  孔洞大小 {hole_size}: {boundaries} 边界边, 变化: 顶点{vertex_change:.1f}%, 面{face_change:.1f}%")
                
                # 更宽松的标准
                if vertex_change <= 40 and face_change <= 50:
                    current_mesh = filled_mesh
                else:
                    print(f"    变化过大，停止中等孔洞填充")
                    break
                    
            except Exception as e:
                print(f"  孔洞大小 {hole_size} 失败: {e}")
                break
    
    # 第三阶段：如果仍然有很多边界边，尝试更大的孔洞
    print("\n第三阶段：大孔洞填充（如果需要）")
    final_boundaries = check_closure(current_mesh)
    
    if final_boundaries > 2000:  # 如果还有很多边界边
        print("尝试更大的孔洞填充...")
        try:
            large_filled = current_mesh.fill_holes(hole_size=150)
            boundaries = check_closure(large_filled)
            
            vertex_change = abs(large_filled.n_points - mesh.n_points) / mesh.n_points * 100
            face_change = abs(large_filled.n_cells - mesh.n_cells) / mesh.n_cells * 100
            
            print(f"  大孔洞填充: {boundaries} 边界边, 变化: 顶点{vertex_change:.1f}%, 面{face_change:.1f}%")
            
            # 如果改善显著且变化可接受
            if boundaries < final_boundaries * 0.7 and vertex_change <= 60:
                current_mesh = large_filled
                print("  ✓ 接受大孔洞填充结果")
            else:
                print("  ✗ 大孔洞填充效果不佳，保持之前结果")
                
        except Exception as e:
            print(f"  大孔洞填充失败: {e}")
    
    # 最终统计
    final_boundaries = check_closure(current_mesh)
    final_vertex_change = abs(current_mesh.n_points - mesh.n_points) / mesh.n_points * 100
    final_face_change = abs(current_mesh.n_cells - mesh.n_cells) / mesh.n_cells * 100
    improvement = (initial_boundaries - final_boundaries) / initial_boundaries * 100
    
    print(f"\n最终结果:")
    print(f"  顶点: {mesh.n_points} → {current_mesh.n_points} (变化 {final_vertex_change:.1f}%)")
    print(f"  面数: {mesh.n_cells} → {current_mesh.n_cells} (变化 {final_face_change:.1f}%)")
    print(f"  边界边: {initial_boundaries} → {final_boundaries} (改善 {improvement:.1f}%)")
    
    return current_mesh

def extract_vertices_faces(mesh):
    """从PyVista网格提取顶点和面"""
    vertices = mesh.points
    faces = []
    
    for i in range(mesh.n_cells):
        cell = mesh.get_cell(i)
        if cell.n_points == 3:
            faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])
    
    return vertices, np.array(faces)

def write_ts_file(filename, vertices, faces, name=None):
    """写入TS文件"""
    if name is None:
        name = os.path.splitext(os.path.basename(filename))[0]
    
    print(f"正在写入TS文件: {filename}")
    
    try:
        with open(filename, "w", encoding='utf-8') as file:
            file.write("GOCAD TSurf 1 \n")
            file.write("HEADER {\n")
            file.write(f"name: {name}\n")
            file.write("*solid*color: #f0e68c\n")
            file.write("use_feature_color: false\n")
            file.write("}\n")
            file.write("GOCAD_ORIGINAL_COORDINATE_SYSTEM\n")
            file.write("NAME \"SKUA Local\"\n")
            file.write("PROJECTION Unknown\n")
            file.write("DATUM Unknown\n")
            file.write("AXIS_NAME X Y Z\n")
            file.write("AXIS_UNIT m m m\n")
            file.write("ZPOSITIVE Elevation\n")
            file.write("END_ORIGINAL_COORDINATE_SYSTEM\n")
            file.write("TFACE\n")
            
            # 写入顶点
            for i, v in enumerate(vertices):
                file.write(f"VRTX {i+1} {v[0]:.9f} {v[1]:.9f} {v[2]:.9f} \n")
            
            # 写入面
            for face in faces:
                file.write(f"TRGL {face[0]+1} {face[1]+1} {face[2]+1}\n")
            
            file.write("END\n")
        
        print(f"✓ 成功写入TS文件: {len(vertices)} 顶点, {len(faces)} 面")
        return True
        
    except Exception as e:
        print(f"✗ 写入TS文件失败: {e}")
        return False

def main():
    """主处理函数"""
    print("=" * 60)
    print("q_1208.ts 平衡闭合脚本")
    print("在形态保持和闭合效果之间找到平衡点")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查输入文件
    input_file = "q_1208.ts"
    if not os.path.exists(input_file):
        print(f"✗ 输入文件 {input_file} 不存在")
        return False
    
    start_time = time.time()
    
    # 步骤1: 读取文件
    print(f"\n步骤1: 读取TS文件...")
    vertices, faces = read_ts_file(input_file)
    
    if len(vertices) == 0 or len(faces) == 0:
        print("✗ 无法读取输入文件")
        return False
    
    # 步骤2: 平衡闭合
    print(f"\n步骤2: 平衡闭合处理...")
    final_mesh = balanced_closure(vertices, faces)
    
    # 步骤3: 保存结果
    print(f"\n步骤3: 保存结果...")
    final_vertices, final_faces = extract_vertices_faces(final_mesh)
    
    output_file = "q_1208_balanced.ts"
    write_success = write_ts_file(output_file, final_vertices, final_faces, "q_1208_balanced")
    
    if not write_success:
        return False
    
    # 统计信息
    end_time = time.time()
    print(f"\n" + "=" * 60)
    print("处理完成:")
    print(f"原始: {len(vertices)} 顶点, {len(faces)} 面")
    print(f"最终: {len(final_vertices)} 顶点, {len(final_faces)} 面")
    
    vertex_change = abs(len(final_vertices) - len(vertices)) / len(vertices) * 100
    face_change = abs(len(final_faces) - len(faces)) / len(faces) * 100
    
    print(f"变化: 顶点 {vertex_change:.1f}%, 面 {face_change:.1f}%")
    print(f"处理时间: {end_time - start_time:.2f} 秒")
    print(f"输出文件: {output_file}")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✓ 平衡闭合脚本执行成功!")
            sys.exit(0)
        else:
            print("\n✗ 平衡闭合脚本执行失败!")
            sys.exit(1)
    except Exception as e:
        print(f"\n未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
