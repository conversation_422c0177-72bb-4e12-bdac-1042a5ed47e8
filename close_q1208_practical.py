#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
q_1208.ts 实用闭合脚本 v5.0
专注于实用的闭合效果，避免过度复杂的方法
"""

import numpy as np
import os
import sys
import time
from datetime import datetime

try:
    import pyvista as pv
    print("✓ PyVista 可用")
except ImportError:
    print("✗ PyVista 不可用，请安装: pip install pyvista")
    sys.exit(1)

def read_ts_file(filename):
    """读取TS文件"""
    print(f"正在读取文件: {filename}")
    
    vertices = []
    faces = []
    vertex_map = {}
    current_idx = 0
    
    encodings = ['utf-8', 'gbk', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            with open(filename, 'r', encoding=encoding) as file:
                lines = file.readlines()
            
            for i, line in enumerate(lines):
                line = line.strip()
                
                if line.startswith('VRTX'):
                    parts = line.split()
                    if len(parts) >= 5:
                        try:
                            vrtx_id = int(parts[1])
                            x, y, z = float(parts[2]), float(parts[3]), float(parts[4])
                            vertices.append([x, y, z])
                            vertex_map[vrtx_id] = current_idx
                            current_idx += 1
                        except (ValueError, IndexError):
                            continue
                
                elif line.startswith('TRGL'):
                    parts = line.split()
                    if len(parts) >= 4:
                        try:
                            v1, v2, v3 = int(parts[1]), int(parts[2]), int(parts[3])
                            if v1 in vertex_map and v2 in vertex_map and v3 in vertex_map:
                                faces.append([vertex_map[v1], vertex_map[v2], vertex_map[v3]])
                        except (ValueError, IndexError):
                            continue
                
                if (i + 1) % 20000 == 0:
                    print(f"已处理 {i + 1} 行...")
            
            vertices = np.array(vertices, dtype=np.float64)
            faces = np.array(faces, dtype=np.int32)
            
            print(f"✓ 成功读取: {len(vertices)} 个顶点, {len(faces)} 个面")
            return vertices, faces
            
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"读取失败 ({encoding}): {e}")
            continue
    
    return np.array([]), np.array([])

def check_closure(mesh):
    """检查网格闭合性"""
    try:
        boundaries = mesh.extract_feature_edges(boundary_edges=True, 
                                               non_manifold_edges=False, 
                                               manifold_edges=False)
        return boundaries.n_cells
    except:
        return -1

def practical_closure(vertices, faces):
    """实用闭合策略 - 专注于可靠的孔洞填充"""
    print("\n=== 实用闭合策略 ===")
    print("目标：在保持合理面数的前提下最大化闭合效果")
    
    # 创建PyVista网格
    faces_with_header = []
    for face in faces:
        faces_with_header.extend([3, face[0], face[1], face[2]])
    
    mesh = pv.PolyData(vertices, faces_with_header)
    print(f"原始网格: {mesh.n_points} 顶点, {mesh.n_cells} 面")
    
    # 记录原始信息
    original_bounds = mesh.bounds
    print(f"原始坐标范围:")
    print(f"  X: [{original_bounds[0]:.2f}, {original_bounds[1]:.2f}]")
    print(f"  Y: [{original_bounds[2]:.2f}, {original_bounds[3]:.2f}]")
    print(f"  Z: [{original_bounds[4]:.2f}, {original_bounds[5]:.2f}]")
    
    # 基本清理
    cleaned_mesh = mesh.clean(tolerance=1e-5)
    print(f"清理后: {cleaned_mesh.n_points} 顶点, {cleaned_mesh.n_cells} 面")
    
    # 检查初始边界
    initial_boundaries = check_closure(cleaned_mesh)
    print(f"初始边界边数: {initial_boundaries}")
    
    if initial_boundaries == 0:
        print("✓ 网格已经闭合")
        return cleaned_mesh
    
    current_mesh = cleaned_mesh
    best_mesh = cleaned_mesh
    best_boundaries = initial_boundaries
    
    # 实用的孔洞填充策略
    print(f"\n开始实用孔洞填充...")
    
    # 分阶段填充，每个阶段都有不同的目标
    stages = [
        {"name": "小孔洞", "sizes": [100, 200, 500, 1000], "max_change": 30},
        {"name": "中孔洞", "sizes": [2000, 5000, 10000], "max_change": 60},
        {"name": "大孔洞", "sizes": [20000, 50000, 100000], "max_change": 100}
    ]
    
    for stage in stages:
        print(f"\n{stage['name']}填充阶段:")
        stage_improved = False
        
        for hole_size in stage["sizes"]:
            try:
                print(f"  尝试孔洞大小: {hole_size}")
                filled_mesh = current_mesh.fill_holes(hole_size=hole_size)
                
                boundaries = check_closure(filled_mesh)
                vertex_change = abs(filled_mesh.n_points - mesh.n_points) / mesh.n_points * 100
                face_change = abs(filled_mesh.n_cells - mesh.n_cells) / mesh.n_cells * 100
                
                print(f"    结果: {boundaries} 边界边")
                print(f"    变化: 顶点 {vertex_change:.1f}%, 面 {face_change:.1f}%")
                print(f"    面数: {filled_mesh.n_cells}")
                
                # 决策逻辑
                improvement = best_boundaries - boundaries
                improvement_rate = improvement / best_boundaries * 100 if best_boundaries > 0 else 0
                
                # 检查是否接受结果
                accept = False
                reason = ""
                
                if boundaries < best_boundaries:  # 有改善
                    if vertex_change <= stage["max_change"] and face_change <= stage["max_change"] * 2:
                        if filled_mesh.n_cells > 1000:  # 确保有足够的面
                            accept = True
                            reason = f"改善 {improvement} 条边界边，变化可接受"
                        else:
                            reason = f"面数太少 ({filled_mesh.n_cells})"
                    else:
                        reason = f"变化过大 (顶点{vertex_change:.1f}%, 面{face_change:.1f}%)"
                else:
                    reason = "无改善"
                
                if accept:
                    current_mesh = filled_mesh
                    best_mesh = filled_mesh
                    best_boundaries = boundaries
                    stage_improved = True
                    print(f"    ✓ 接受结果: {reason}")
                    
                    # 如果已经很好了，可以提前结束这个阶段
                    if boundaries < 1000:
                        print(f"    🎉 边界边已减少到 {boundaries}，提前结束此阶段")
                        break
                else:
                    print(f"    ✗ 拒绝结果: {reason}")
                    
            except Exception as e:
                print(f"    孔洞大小 {hole_size} 失败: {e}")
                continue
        
        # 如果这个阶段没有改善，考虑是否继续
        if not stage_improved:
            print(f"  {stage['name']}阶段无改善，跳过后续更大的孔洞")
            break
        else:
            print(f"  {stage['name']}阶段完成，当前边界边: {best_boundaries}")
    
    # 最终尝试：如果边界边还是很多，尝试一次性大填充
    final_boundaries = check_closure(best_mesh)
    if final_boundaries > 2000:
        print(f"\n最终尝试：超大孔洞填充")
        try:
            mega_filled = current_mesh.fill_holes(hole_size=200000)
            boundaries = check_closure(mega_filled)
            
            vertex_change = abs(mega_filled.n_points - mesh.n_points) / mesh.n_points * 100
            face_change = abs(mega_filled.n_cells - mesh.n_cells) / mesh.n_cells * 100
            
            print(f"  超大填充: {boundaries} 边界边, {mega_filled.n_cells} 面")
            print(f"  变化: 顶点 {vertex_change:.1f}%, 面 {face_change:.1f}%")
            
            # 如果改善显著且面数合理
            if boundaries < final_boundaries * 0.5 and mega_filled.n_cells > 5000:
                best_mesh = mega_filled
                final_boundaries = boundaries
                print(f"  ✓ 接受超大填充结果")
            else:
                print(f"  ✗ 超大填充效果不佳")
                
        except Exception as e:
            print(f"  超大填充失败: {e}")
    
    # 最终统计
    final_boundaries = check_closure(best_mesh)
    final_vertex_change = abs(best_mesh.n_points - mesh.n_points) / mesh.n_points * 100
    final_face_change = abs(best_mesh.n_cells - mesh.n_cells) / mesh.n_cells * 100
    total_improvement = (initial_boundaries - final_boundaries) / initial_boundaries * 100
    
    print(f"\n" + "="*60)
    print(f"实用闭合最终结果:")
    print(f"  顶点: {mesh.n_points} → {best_mesh.n_points} (变化 {final_vertex_change:.1f}%)")
    print(f"  面数: {mesh.n_cells} → {best_mesh.n_cells} (变化 {final_face_change:.1f}%)")
    print(f"  边界边: {initial_boundaries} → {final_boundaries} (改善 {total_improvement:.1f}%)")
    
    # 评估结果
    if final_boundaries == 0:
        print(f"🎉 完全闭合成功！")
    elif final_boundaries < 100:
        print(f"✓ 优秀闭合！边界边已减少到 {final_boundaries}")
    elif final_boundaries < 500:
        print(f"✓ 良好闭合！边界边已减少到 {final_boundaries}")
    elif final_boundaries < 2000:
        print(f"✓ 基本闭合！边界边已减少到 {final_boundaries}")
    elif total_improvement > 50:
        print(f"✓ 显著改善！边界边减少了 {total_improvement:.1f}%")
    else:
        print(f"⚠ 改善有限，可能需要其他方法")
    
    print("="*60)
    
    return best_mesh

def extract_vertices_faces(mesh):
    """从PyVista网格提取顶点和面"""
    vertices = mesh.points
    faces = []
    
    for i in range(mesh.n_cells):
        cell = mesh.get_cell(i)
        if cell.n_points == 3:
            faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])
    
    return vertices, np.array(faces)

def write_ts_file(filename, vertices, faces, name=None):
    """写入TS文件"""
    if name is None:
        name = os.path.splitext(os.path.basename(filename))[0]
    
    print(f"正在写入TS文件: {filename}")
    
    try:
        with open(filename, "w", encoding='utf-8') as file:
            file.write("GOCAD TSurf 1 \n")
            file.write("HEADER {\n")
            file.write(f"name: {name}\n")
            file.write("*solid*color: #f0e68c\n")
            file.write("use_feature_color: false\n")
            file.write("}\n")
            file.write("GOCAD_ORIGINAL_COORDINATE_SYSTEM\n")
            file.write("NAME \"SKUA Local\"\n")
            file.write("PROJECTION Unknown\n")
            file.write("DATUM Unknown\n")
            file.write("AXIS_NAME X Y Z\n")
            file.write("AXIS_UNIT m m m\n")
            file.write("ZPOSITIVE Elevation\n")
            file.write("END_ORIGINAL_COORDINATE_SYSTEM\n")
            file.write("TFACE\n")
            
            # 写入顶点
            for i, v in enumerate(vertices):
                file.write(f"VRTX {i+1} {v[0]:.9f} {v[1]:.9f} {v[2]:.9f} \n")
            
            # 写入面
            for face in faces:
                file.write(f"TRGL {face[0]+1} {face[1]+1} {face[2]+1}\n")
            
            file.write("END\n")
        
        print(f"✓ 成功写入TS文件: {len(vertices)} 顶点, {len(faces)} 面")
        return True
        
    except Exception as e:
        print(f"✗ 写入TS文件失败: {e}")
        return False

def main():
    """主处理函数"""
    print("=" * 60)
    print("q_1208.ts 实用闭合脚本 v5.0")
    print("专注于实用的闭合效果，避免过度复杂的方法")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查输入文件
    input_file = "q_1208.ts"
    if not os.path.exists(input_file):
        print(f"✗ 输入文件 {input_file} 不存在")
        return False
    
    start_time = time.time()
    
    # 步骤1: 读取文件
    print(f"\n步骤1: 读取TS文件...")
    vertices, faces = read_ts_file(input_file)
    
    if len(vertices) == 0 or len(faces) == 0:
        print("✗ 无法读取输入文件")
        return False
    
    # 步骤2: 实用闭合
    print(f"\n步骤2: 实用闭合处理...")
    final_mesh = practical_closure(vertices, faces)
    
    # 步骤3: 保存结果
    print(f"\n步骤3: 保存结果...")
    final_vertices, final_faces = extract_vertices_faces(final_mesh)
    
    output_file = "q_1208_practical.ts"
    write_success = write_ts_file(output_file, final_vertices, final_faces, "q_1208_practical")
    
    if not write_success:
        return False
    
    # 统计信息
    end_time = time.time()
    print(f"\n" + "=" * 60)
    print("处理完成:")
    print(f"原始: {len(vertices)} 顶点, {len(faces)} 面")
    print(f"最终: {len(final_vertices)} 顶点, {len(final_faces)} 面")
    
    vertex_change = abs(len(final_vertices) - len(vertices)) / len(vertices) * 100
    face_change = abs(len(final_faces) - len(faces)) / len(faces) * 100
    
    print(f"变化: 顶点 {vertex_change:.1f}%, 面 {face_change:.1f}%")
    print(f"处理时间: {end_time - start_time:.2f} 秒")
    print(f"输出文件: {output_file}")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✓ 实用闭合脚本执行成功!")
            sys.exit(0)
        else:
            print("\n✗ 实用闭合脚本执行失败!")
            sys.exit(1)
    except Exception as e:
        print(f"\n未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
