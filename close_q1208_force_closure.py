#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
q_1208.ts 强力闭合脚本 v4.0
目标：实现真正的闭合（边界边接近0）
接受必要的形态变化来换取完全闭合
"""

import numpy as np
import os
import sys
import time
from datetime import datetime

try:
    import pyvista as pv
    print("✓ PyVista 可用")
except ImportError:
    print("✗ PyVista 不可用，请安装: pip install pyvista")
    sys.exit(1)

def read_ts_file(filename):
    """读取TS文件"""
    print(f"正在读取文件: {filename}")
    
    vertices = []
    faces = []
    vertex_map = {}
    current_idx = 0
    
    encodings = ['utf-8', 'gbk', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            with open(filename, 'r', encoding=encoding) as file:
                lines = file.readlines()
            
            for i, line in enumerate(lines):
                line = line.strip()
                
                if line.startswith('VRTX'):
                    parts = line.split()
                    if len(parts) >= 5:
                        try:
                            vrtx_id = int(parts[1])
                            x, y, z = float(parts[2]), float(parts[3]), float(parts[4])
                            vertices.append([x, y, z])
                            vertex_map[vrtx_id] = current_idx
                            current_idx += 1
                        except (ValueError, IndexError):
                            continue
                
                elif line.startswith('TRGL'):
                    parts = line.split()
                    if len(parts) >= 4:
                        try:
                            v1, v2, v3 = int(parts[1]), int(parts[2]), int(parts[3])
                            if v1 in vertex_map and v2 in vertex_map and v3 in vertex_map:
                                faces.append([vertex_map[v1], vertex_map[v2], vertex_map[v3]])
                        except (ValueError, IndexError):
                            continue
                
                if (i + 1) % 20000 == 0:
                    print(f"已处理 {i + 1} 行...")
            
            vertices = np.array(vertices, dtype=np.float64)
            faces = np.array(faces, dtype=np.int32)
            
            print(f"✓ 成功读取: {len(vertices)} 个顶点, {len(faces)} 个面")
            return vertices, faces
            
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"读取失败 ({encoding}): {e}")
            continue
    
    return np.array([]), np.array([])

def check_closure(mesh):
    """检查网格闭合性"""
    try:
        boundaries = mesh.extract_feature_edges(boundary_edges=True, 
                                               non_manifold_edges=False, 
                                               manifold_edges=False)
        return boundaries.n_cells
    except:
        return -1

def force_closure(vertices, faces):
    """强力闭合策略 - 目标是实现真正的闭合"""
    print("\n=== 强力闭合策略 ===")
    print("目标：实现真正的闭合（边界边接近0）")
    
    # 创建PyVista网格
    faces_with_header = []
    for face in faces:
        faces_with_header.extend([3, face[0], face[1], face[2]])
    
    mesh = pv.PolyData(vertices, faces_with_header)
    print(f"原始网格: {mesh.n_points} 顶点, {mesh.n_cells} 面")
    
    # 记录原始信息
    original_bounds = mesh.bounds
    original_volume = 0
    try:
        if mesh.is_all_triangles:
            original_volume = mesh.volume
    except:
        pass
    
    print(f"原始坐标范围:")
    print(f"  X: [{original_bounds[0]:.2f}, {original_bounds[1]:.2f}]")
    print(f"  Y: [{original_bounds[2]:.2f}, {original_bounds[3]:.2f}]")
    print(f"  Z: [{original_bounds[4]:.2f}, {original_bounds[5]:.2f}]")
    
    # 基本清理
    cleaned_mesh = mesh.clean(tolerance=1e-5)
    print(f"清理后: {cleaned_mesh.n_points} 顶点, {cleaned_mesh.n_cells} 面")
    
    # 检查初始边界
    initial_boundaries = check_closure(cleaned_mesh)
    print(f"初始边界边数: {initial_boundaries}")
    
    if initial_boundaries == 0:
        print("✓ 网格已经闭合")
        return cleaned_mesh
    
    current_mesh = cleaned_mesh
    
    # 第一阶段：激进的孔洞填充
    print(f"\n第一阶段：激进孔洞填充")
    hole_sizes = [1000, 2000, 5000, 10000, 20000, 50000]
    
    for hole_size in hole_sizes:
        try:
            print(f"\n尝试孔洞大小: {hole_size}")
            filled_mesh = current_mesh.fill_holes(hole_size=hole_size)
            
            boundaries = check_closure(filled_mesh)
            vertex_change = abs(filled_mesh.n_points - mesh.n_points) / mesh.n_points * 100
            face_change = abs(filled_mesh.n_cells - mesh.n_cells) / mesh.n_cells * 100
            
            print(f"  结果: {boundaries} 边界边")
            print(f"  变化: 顶点 {vertex_change:.1f}%, 面 {face_change:.1f}%")
            
            if boundaries < initial_boundaries:
                current_mesh = filled_mesh
                print(f"  ✓ 接受结果（改善了 {initial_boundaries - boundaries} 条边界边）")
                
                # 如果已经很好了，可以停止
                if boundaries < 50:
                    print(f"  🎉 边界边已减少到 {boundaries}，基本闭合！")
                    break
            else:
                print(f"  - 无改善，继续尝试")
                
        except Exception as e:
            print(f"  孔洞大小 {hole_size} 失败: {e}")
            continue
    
    # 检查第一阶段结果
    stage1_boundaries = check_closure(current_mesh)
    print(f"\n第一阶段结果: {stage1_boundaries} 边界边")
    
    # 第二阶段：如果还不够闭合，尝试更强力的方法
    if stage1_boundaries > 100:
        print(f"\n第二阶段：强力闭合方法")
        
        # 方法1：尝试convex_hull
        print("尝试凸包方法...")
        try:
            hull_mesh = current_mesh.convex_hull()
            hull_boundaries = check_closure(hull_mesh)
            
            vertex_change = abs(hull_mesh.n_points - mesh.n_points) / mesh.n_points * 100
            face_change = abs(hull_mesh.n_cells - mesh.n_cells) / mesh.n_cells * 100
            
            print(f"  凸包结果: {hull_boundaries} 边界边")
            print(f"  变化: 顶点 {vertex_change:.1f}%, 面 {face_change:.1f}%")
            
            if hull_boundaries == 0:
                print("  ✓ 凸包实现完全闭合！")
                # 检查形态变化是否可接受
                bounds_change = max(
                    abs(hull_mesh.bounds[i] - original_bounds[i]) / abs(original_bounds[i]) 
                    for i in range(6) if original_bounds[i] != 0
                )
                
                if bounds_change < 0.5:  # 坐标范围变化<50%
                    current_mesh = hull_mesh
                    print("  ✓ 接受凸包结果")
                else:
                    print("  ✗ 凸包形态变化过大，尝试其他方法")
            
        except Exception as e:
            print(f"  凸包方法失败: {e}")
        
        # 方法2：如果凸包不行，尝试alpha_shape
        current_boundaries = check_closure(current_mesh)
        if current_boundaries > 50:
            print("\n尝试Alpha Shape方法...")
            try:
                # 提取点云
                points = current_mesh.points

                # 尝试不同的alpha值
                alpha_values = [0.1, 0.5, 1.0, 2.0, 5.0]

                for alpha in alpha_values:
                    try:
                        alpha_mesh = pv.PolyData(points).delaunay_3d(alpha=alpha)
                        if alpha_mesh.n_cells > 0:
                            surface = alpha_mesh.extract_surface()
                            boundaries = check_closure(surface)

                            print(f"  Alpha {alpha}: {boundaries} 边界边, {surface.n_cells} 面")

                            # 只有当面数合理且边界边改善时才接受
                            if boundaries < current_boundaries and surface.n_cells > 1000:
                                vertex_change = abs(surface.n_points - mesh.n_points) / mesh.n_points * 100
                                if vertex_change < 200:  # 允许较大变化
                                    current_mesh = surface
                                    print(f"    ✓ 接受Alpha Shape结果")
                                    break
                                else:
                                    print(f"    - 顶点变化过大 ({vertex_change:.1f}%)")
                            else:
                                print(f"    - 面数太少或无改善")
                    except:
                        continue

            except Exception as e:
                print(f"  Alpha Shape方法失败: {e}")
    
    # 第三阶段：最后的备选方案
    final_boundaries = check_closure(current_mesh)
    if final_boundaries > 100:
        print(f"\n第三阶段：最后备选方案")
        print("尝试Delaunay 3D重建...")
        
        try:
            # 使用原始顶点进行Delaunay 3D
            points_mesh = pv.PolyData(mesh.points)
            tet_mesh = points_mesh.delaunay_3d(alpha=0, tol=1e-3, offset=10.0)
            
            if tet_mesh.n_cells > 0:
                surface = tet_mesh.extract_surface()
                boundaries = check_closure(surface)
                
                print(f"  Delaunay 3D结果: {boundaries} 边界边")
                
                if boundaries < final_boundaries:
                    current_mesh = surface
                    print("  ✓ 接受Delaunay 3D结果")
                    
        except Exception as e:
            print(f"  Delaunay 3D失败: {e}")
    
    # 最终统计
    final_boundaries = check_closure(current_mesh)
    final_vertex_change = abs(current_mesh.n_points - mesh.n_points) / mesh.n_points * 100
    final_face_change = abs(current_mesh.n_cells - mesh.n_cells) / mesh.n_cells * 100
    total_improvement = (initial_boundaries - final_boundaries) / initial_boundaries * 100
    
    print(f"\n" + "="*60)
    print(f"强力闭合最终结果:")
    print(f"  顶点: {mesh.n_points} → {current_mesh.n_points} (变化 {final_vertex_change:.1f}%)")
    print(f"  面数: {mesh.n_cells} → {current_mesh.n_cells} (变化 {final_face_change:.1f}%)")
    print(f"  边界边: {initial_boundaries} → {final_boundaries} (改善 {total_improvement:.1f}%)")
    
    if final_boundaries == 0:
        print(f"🎉 完全闭合成功！网格已完全闭合")
    elif final_boundaries < 50:
        print(f"✓ 基本闭合成功！边界边已减少到可接受范围")
    elif total_improvement > 50:
        print(f"✓ 显著改善！边界边减少了一半以上")
    else:
        print(f"⚠ 改善有限，这个网格可能需要手动修复")
    
    print("="*60)
    
    return current_mesh

def extract_vertices_faces(mesh):
    """从PyVista网格提取顶点和面"""
    vertices = mesh.points
    faces = []
    
    for i in range(mesh.n_cells):
        cell = mesh.get_cell(i)
        if cell.n_points == 3:
            faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])
    
    return vertices, np.array(faces)

def write_ts_file(filename, vertices, faces, name=None):
    """写入TS文件"""
    if name is None:
        name = os.path.splitext(os.path.basename(filename))[0]
    
    print(f"正在写入TS文件: {filename}")
    
    try:
        with open(filename, "w", encoding='utf-8') as file:
            file.write("GOCAD TSurf 1 \n")
            file.write("HEADER {\n")
            file.write(f"name: {name}\n")
            file.write("*solid*color: #f0e68c\n")
            file.write("use_feature_color: false\n")
            file.write("}\n")
            file.write("GOCAD_ORIGINAL_COORDINATE_SYSTEM\n")
            file.write("NAME \"SKUA Local\"\n")
            file.write("PROJECTION Unknown\n")
            file.write("DATUM Unknown\n")
            file.write("AXIS_NAME X Y Z\n")
            file.write("AXIS_UNIT m m m\n")
            file.write("ZPOSITIVE Elevation\n")
            file.write("END_ORIGINAL_COORDINATE_SYSTEM\n")
            file.write("TFACE\n")
            
            # 写入顶点
            for i, v in enumerate(vertices):
                file.write(f"VRTX {i+1} {v[0]:.9f} {v[1]:.9f} {v[2]:.9f} \n")
            
            # 写入面
            for face in faces:
                file.write(f"TRGL {face[0]+1} {face[1]+1} {face[2]+1}\n")
            
            file.write("END\n")
        
        print(f"✓ 成功写入TS文件: {len(vertices)} 顶点, {len(faces)} 面")
        return True
        
    except Exception as e:
        print(f"✗ 写入TS文件失败: {e}")
        return False

def main():
    """主处理函数"""
    print("=" * 60)
    print("q_1208.ts 强力闭合脚本 v4.0")
    print("目标：实现真正的闭合（边界边接近0）")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查输入文件
    input_file = "q_1208.ts"
    if not os.path.exists(input_file):
        print(f"✗ 输入文件 {input_file} 不存在")
        return False
    
    start_time = time.time()
    
    # 步骤1: 读取文件
    print(f"\n步骤1: 读取TS文件...")
    vertices, faces = read_ts_file(input_file)
    
    if len(vertices) == 0 or len(faces) == 0:
        print("✗ 无法读取输入文件")
        return False
    
    # 步骤2: 强力闭合
    print(f"\n步骤2: 强力闭合处理...")
    final_mesh = force_closure(vertices, faces)
    
    # 步骤3: 保存结果
    print(f"\n步骤3: 保存结果...")
    final_vertices, final_faces = extract_vertices_faces(final_mesh)
    
    output_file = "q_1208_force_closed.ts"
    write_success = write_ts_file(output_file, final_vertices, final_faces, "q_1208_force_closed")
    
    if not write_success:
        return False
    
    # 统计信息
    end_time = time.time()
    print(f"\n" + "=" * 60)
    print("处理完成:")
    print(f"原始: {len(vertices)} 顶点, {len(faces)} 面")
    print(f"最终: {len(final_vertices)} 顶点, {len(final_faces)} 面")
    
    vertex_change = abs(len(final_vertices) - len(vertices)) / len(vertices) * 100
    face_change = abs(len(final_faces) - len(faces)) / len(faces) * 100
    
    print(f"变化: 顶点 {vertex_change:.1f}%, 面 {face_change:.1f}%")
    print(f"处理时间: {end_time - start_time:.2f} 秒")
    print(f"输出文件: {output_file}")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✓ 强力闭合脚本执行成功!")
            sys.exit(0)
        else:
            print("\n✗ 强力闭合脚本执行失败!")
            sys.exit(1)
    except Exception as e:
        print(f"\n未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
