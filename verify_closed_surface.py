#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证生成的闭合表面TS文件
"""

import numpy as np
import os

def read_tsurf_data(file_path: str):
    """读取 GOCAD TSurf 格式文件"""
    print(f"正在读取 TS 文件: {file_path}")
    
    vertices = []
    vertex_map = {}
    faces = []
    current_idx = 0
    
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            
            if line.startswith('VRTX'):
                parts = line.split()
                if len(parts) >= 5:
                    vertex_id = int(parts[1])
                    x, y, z = float(parts[2]), float(parts[3]), float(parts[4])
                    vertices.append([x, y, z])
                    vertex_map[vertex_id] = current_idx
                    current_idx += 1
            
            elif line.startswith('TRGL'):
                parts = line.split()
                if len(parts) >= 4:
                    v1, v2, v3 = int(parts[1]), int(parts[2]), int(parts[3])
                    if v1 in vertex_map and v2 in vertex_map and v3 in vertex_map:
                        faces.append([vertex_map[v1], vertex_map[v2], vertex_map[v3]])
    
    vertices = np.array(vertices, dtype=np.float64)
    faces = np.array(faces, dtype=np.int32)
    
    print(f"读取完成: {len(vertices)} 个顶点, {len(faces)} 个面")
    return vertices, faces

def check_closure(vertices, faces):
    """检查网格是否闭合"""
    print("\n=== 闭合性验证 ===")
    
    # 统计边的使用次数
    edge_count = {}
    
    for face in faces:
        edges = [
            tuple(sorted([face[0], face[1]])),
            tuple(sorted([face[1], face[2]])),
            tuple(sorted([face[2], face[0]]))
        ]
        
        for edge in edges:
            edge_count[edge] = edge_count.get(edge, 0) + 1
    
    # 分析边的类型
    boundary_edges = 0
    internal_edges = 0
    problematic_edges = 0
    
    for edge, count in edge_count.items():
        if count == 1:
            boundary_edges += 1
        elif count == 2:
            internal_edges += 1
        else:
            problematic_edges += 1
    
    # 计算欧拉特征数
    V = len(vertices)
    E = len(edge_count)
    F = len(faces)
    euler_char = V - E + F
    
    print(f"顶点数 (V): {V}")
    print(f"边数 (E): {E}")
    print(f"面数 (F): {F}")
    print(f"欧拉特征数 (V-E+F): {euler_char}")
    print(f"边界边数: {boundary_edges}")
    print(f"内部边数: {internal_edges}")
    print(f"问题边数: {problematic_edges}")
    
    is_closed = boundary_edges == 0
    is_manifold = problematic_edges == 0
    
    print(f"\n结果:")
    print(f"是否闭合: {'✓ 是' if is_closed else '✗ 否'}")
    print(f"是否流形: {'✓ 是' if is_manifold else '✗ 否'}")
    
    # 对于闭合表面，欧拉特征数应该是2（球面）或其他已知值
    if is_closed:
        if euler_char == 2:
            print("✓ 欧拉特征数为2，符合球面拓扑")
        else:
            print(f"⚠️ 欧拉特征数为{euler_char}，可能是其他拓扑结构")
    
    return {
        'is_closed': is_closed,
        'is_manifold': is_manifold,
        'boundary_edges': boundary_edges,
        'euler_characteristic': euler_char,
        'vertices': V,
        'faces': F,
        'edges': E
    }

def calculate_volume_and_area(vertices, faces):
    """计算体积和表面积"""
    print("\n=== 几何属性计算 ===")
    
    # 计算表面积
    total_area = 0.0
    for face in faces:
        v0, v1, v2 = vertices[face[0]], vertices[face[1]], vertices[face[2]]
        # 使用叉积计算三角形面积
        edge1 = v1 - v0
        edge2 = v2 - v0
        cross = np.cross(edge1, edge2)
        area = 0.5 * np.linalg.norm(cross)
        total_area += area
    
    # 计算体积（使用散度定理）
    total_volume = 0.0
    origin = np.array([0.0, 0.0, 0.0])
    
    for face in faces:
        v0, v1, v2 = vertices[face[0]], vertices[face[1]], vertices[face[2]]
        # 计算四面体体积（原点到三角形）
        volume = np.dot(v0, np.cross(v1, v2)) / 6.0
        total_volume += volume
    
    print(f"表面积: {total_area:.2f} 平方米")
    print(f"体积: {abs(total_volume):.2f} 立方米")
    
    return total_area, abs(total_volume)

def main():
    """主函数"""
    print("=" * 60)
    print("闭合表面TS文件验证")
    print("=" * 60)
    
    # 验证原始文件和闭合文件
    files_to_check = [
        ("q_1208.ts", "原始文件"),
        ("q_1208_closed_surface.ts", "闭合表面文件")
    ]
    
    results = {}
    
    for filename, description in files_to_check:
        if not os.path.exists(filename):
            print(f"⚠️ 文件 {filename} 不存在，跳过")
            continue
            
        print(f"\n{'='*20} {description} {'='*20}")
        
        try:
            vertices, faces = read_tsurf_data(filename)
            closure_result = check_closure(vertices, faces)
            
            if closure_result['is_closed']:
                area, volume = calculate_volume_and_area(vertices, faces)
                closure_result['surface_area'] = area
                closure_result['volume'] = volume
            
            results[filename] = closure_result
            
        except Exception as e:
            print(f"✗ 处理文件 {filename} 时出错: {e}")
    
    # 对比结果
    if len(results) >= 2:
        print(f"\n{'='*60}")
        print("对比结果")
        print("=" * 60)
        
        original = results.get("q_1208.ts")
        closed = results.get("q_1208_closed_surface.ts")
        
        if original and closed:
            print("原始文件 vs 闭合文件:")
            print(f"  顶点数: {original['vertices']} → {closed['vertices']}")
            print(f"  面数: {original['faces']} → {closed['faces']}")
            print(f"  边界边: {original['boundary_edges']} → {closed['boundary_edges']}")
            print(f"  闭合性: {'否' if not original['is_closed'] else '是'} → {'是' if closed['is_closed'] else '否'}")
            
            if closed['is_closed']:
                print(f"  表面积: {closed.get('surface_area', 'N/A'):.2f} 平方米")
                print(f"  体积: {closed.get('volume', 'N/A'):.2f} 立方米")
    
    print(f"\n验证完成!")

if __name__ == "__main__":
    main()
