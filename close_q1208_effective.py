#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
q_1208.ts 有效闭合脚本 v3.0
在保持主要形态特征的前提下实现有效闭合
"""

import numpy as np
import os
import sys
import time
from datetime import datetime

try:
    import pyvista as pv
    print("✓ PyVista 可用")
except ImportError:
    print("✗ PyVista 不可用，请安装: pip install pyvista")
    sys.exit(1)

def read_ts_file(filename):
    """读取TS文件"""
    print(f"正在读取文件: {filename}")
    
    vertices = []
    faces = []
    vertex_map = {}
    current_idx = 0
    
    encodings = ['utf-8', 'gbk', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            with open(filename, 'r', encoding=encoding) as file:
                lines = file.readlines()
            
            for i, line in enumerate(lines):
                line = line.strip()
                
                if line.startswith('VRTX'):
                    parts = line.split()
                    if len(parts) >= 5:
                        try:
                            vrtx_id = int(parts[1])
                            x, y, z = float(parts[2]), float(parts[3]), float(parts[4])
                            vertices.append([x, y, z])
                            vertex_map[vrtx_id] = current_idx
                            current_idx += 1
                        except (ValueError, IndexError):
                            continue
                
                elif line.startswith('TRGL'):
                    parts = line.split()
                    if len(parts) >= 4:
                        try:
                            v1, v2, v3 = int(parts[1]), int(parts[2]), int(parts[3])
                            if v1 in vertex_map and v2 in vertex_map and v3 in vertex_map:
                                faces.append([vertex_map[v1], vertex_map[v2], vertex_map[v3]])
                        except (ValueError, IndexError):
                            continue
                
                if (i + 1) % 20000 == 0:
                    print(f"已处理 {i + 1} 行...")
            
            vertices = np.array(vertices, dtype=np.float64)
            faces = np.array(faces, dtype=np.int32)
            
            print(f"✓ 成功读取: {len(vertices)} 个顶点, {len(faces)} 个面")
            return vertices, faces
            
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"读取失败 ({encoding}): {e}")
            continue
    
    return np.array([]), np.array([])

def check_closure_and_bounds(mesh, original_bounds=None):
    """检查网格闭合性和坐标范围"""
    try:
        boundaries = mesh.extract_feature_edges(boundary_edges=True, 
                                               non_manifold_edges=False, 
                                               manifold_edges=False)
        
        # 检查坐标范围
        bounds = mesh.bounds  # [xmin, xmax, ymin, ymax, zmin, zmax]
        bounds_preserved = True
        
        if original_bounds is not None:
            # 检查坐标范围是否基本保持（允许5%的扩展）
            for i in range(0, 6, 2):  # xmin, ymin, zmin
                if bounds[i] < original_bounds[i] * 0.95:
                    bounds_preserved = False
                    break
            for i in range(1, 6, 2):  # xmax, ymax, zmax
                if bounds[i] > original_bounds[i] * 1.05:
                    bounds_preserved = False
                    break
        
        return boundaries.n_cells, bounds_preserved, bounds
    except:
        return -1, False, None

def effective_closure(vertices, faces):
    """有效闭合策略 - 在形态保持和闭合效果间平衡"""
    print("\n=== 有效闭合策略 ===")
    
    # 创建PyVista网格
    faces_with_header = []
    for face in faces:
        faces_with_header.extend([3, face[0], face[1], face[2]])
    
    mesh = pv.PolyData(vertices, faces_with_header)
    print(f"原始网格: {mesh.n_points} 顶点, {mesh.n_cells} 面")
    
    # 记录原始坐标范围
    original_bounds = mesh.bounds
    print(f"原始坐标范围:")
    print(f"  X: [{original_bounds[0]:.2f}, {original_bounds[1]:.2f}]")
    print(f"  Y: [{original_bounds[2]:.2f}, {original_bounds[3]:.2f}]")
    print(f"  Z: [{original_bounds[4]:.2f}, {original_bounds[5]:.2f}]")
    
    # 轻微清理
    cleaned_mesh = mesh.clean(tolerance=1e-5)
    print(f"清理后: {cleaned_mesh.n_points} 顶点, {cleaned_mesh.n_cells} 面")
    
    # 检查初始边界
    initial_boundaries, _, _ = check_closure_and_bounds(cleaned_mesh, original_bounds)
    print(f"初始边界边数: {initial_boundaries}")
    
    if initial_boundaries == 0:
        print("✓ 网格已经闭合")
        return cleaned_mesh
    
    # 有效闭合策略 - 更积极的参数
    current_mesh = cleaned_mesh
    best_mesh = cleaned_mesh
    best_boundaries = initial_boundaries
    
    # 孔洞填充参数 - 从小到大
    hole_sizes = [5, 10, 25, 50, 100, 200, 500, 1000]
    
    print(f"\n开始渐进式孔洞填充...")
    
    for hole_size in hole_sizes:
        try:
            print(f"\n尝试孔洞大小: {hole_size}")
            filled_mesh = current_mesh.fill_holes(hole_size=hole_size)
            
            # 检查结果
            boundaries, bounds_ok, new_bounds = check_closure_and_bounds(filled_mesh, original_bounds)
            
            # 计算变化
            vertex_change = abs(filled_mesh.n_points - mesh.n_points) / mesh.n_points * 100
            face_change = abs(filled_mesh.n_cells - mesh.n_cells) / mesh.n_cells * 100
            boundary_improvement = initial_boundaries - boundaries
            improvement_rate = boundary_improvement / initial_boundaries * 100
            
            print(f"  结果: {boundaries} 边界边 (改善 {improvement_rate:.1f}%)")
            print(f"  变化: 顶点 {vertex_change:.1f}%, 面 {face_change:.1f}%")
            print(f"  坐标范围: {'✓' if bounds_ok else '✗'}")
            
            # 决策逻辑：优先考虑闭合效果，但要保持坐标范围
            accept_result = False
            
            if bounds_ok:  # 坐标范围必须保持
                if boundaries < best_boundaries:  # 有改善
                    if vertex_change <= 60 and face_change <= 150:  # 宽松的变化限制
                        accept_result = True
                        print(f"  ✓ 接受结果（显著改善且变化可接受）")
                    elif improvement_rate >= 20:  # 如果改善很大，允许更多变化
                        accept_result = True
                        print(f"  ✓ 接受结果（改善显著，允许较大变化）")
                    else:
                        print(f"  - 改善有限，继续尝试")
                else:
                    print(f"  - 无改善，继续尝试")
            else:
                print(f"  ✗ 坐标范围变化过大，拒绝")
            
            if accept_result:
                current_mesh = filled_mesh
                best_mesh = filled_mesh
                best_boundaries = boundaries
                
                # 如果已经很好了，可以提前停止
                if boundaries < 1000:
                    print(f"  🎉 边界边已减少到 {boundaries}，效果良好！")
                    break
            else:
                # 如果这个孔洞大小没有改善，尝试下一个
                continue
                
        except Exception as e:
            print(f"  孔洞大小 {hole_size} 失败: {e}")
            continue
    
    # 如果还是有很多边界边，尝试更激进的方法
    final_boundaries, _, _ = check_closure_and_bounds(best_mesh, original_bounds)
    
    if final_boundaries > 2000:
        print(f"\n边界边仍然较多 ({final_boundaries})，尝试更激进的方法...")
        
        try:
            # 尝试更大的孔洞填充
            aggressive_mesh = best_mesh.fill_holes(hole_size=2000)
            boundaries, bounds_ok, _ = check_closure_and_bounds(aggressive_mesh, original_bounds)
            
            vertex_change = abs(aggressive_mesh.n_points - mesh.n_points) / mesh.n_points * 100
            face_change = abs(aggressive_mesh.n_cells - mesh.n_cells) / mesh.n_cells * 100
            
            print(f"  激进填充: {boundaries} 边界边, 变化: 顶点{vertex_change:.1f}%, 面{face_change:.1f}%")
            
            if bounds_ok and boundaries < final_boundaries * 0.5:  # 改善50%以上
                best_mesh = aggressive_mesh
                final_boundaries = boundaries
                print(f"  ✓ 接受激进填充结果")
            else:
                print(f"  ✗ 激进填充效果不佳或破坏形态")
                
        except Exception as e:
            print(f"  激进填充失败: {e}")
    
    # 最终统计
    final_boundaries, bounds_ok, final_bounds = check_closure_and_bounds(best_mesh, original_bounds)
    final_vertex_change = abs(best_mesh.n_points - mesh.n_points) / mesh.n_points * 100
    final_face_change = abs(best_mesh.n_cells - mesh.n_cells) / mesh.n_cells * 100
    total_improvement = (initial_boundaries - final_boundaries) / initial_boundaries * 100
    
    print(f"\n" + "="*50)
    print(f"最终结果:")
    print(f"  顶点: {mesh.n_points} → {best_mesh.n_points} (变化 {final_vertex_change:.1f}%)")
    print(f"  面数: {mesh.n_cells} → {best_mesh.n_cells} (变化 {final_face_change:.1f}%)")
    print(f"  边界边: {initial_boundaries} → {final_boundaries} (改善 {total_improvement:.1f}%)")
    print(f"  坐标范围: {'✓ 保持' if bounds_ok else '✗ 变化'}")
    
    if final_boundaries < 2000:
        print(f"✓ 有效闭合成功！边界边已减少到可接受范围")
    elif total_improvement > 30:
        print(f"✓ 部分成功，显著改善了闭合性")
    else:
        print(f"⚠ 闭合改善有限，可能需要其他方法")
    
    print("="*50)
    
    return best_mesh

def extract_vertices_faces(mesh):
    """从PyVista网格提取顶点和面"""
    vertices = mesh.points
    faces = []
    
    for i in range(mesh.n_cells):
        cell = mesh.get_cell(i)
        if cell.n_points == 3:
            faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])
    
    return vertices, np.array(faces)

def write_ts_file(filename, vertices, faces, name=None):
    """写入TS文件"""
    if name is None:
        name = os.path.splitext(os.path.basename(filename))[0]
    
    print(f"正在写入TS文件: {filename}")
    
    try:
        with open(filename, "w", encoding='utf-8') as file:
            file.write("GOCAD TSurf 1 \n")
            file.write("HEADER {\n")
            file.write(f"name: {name}\n")
            file.write("*solid*color: #f0e68c\n")
            file.write("use_feature_color: false\n")
            file.write("}\n")
            file.write("GOCAD_ORIGINAL_COORDINATE_SYSTEM\n")
            file.write("NAME \"SKUA Local\"\n")
            file.write("PROJECTION Unknown\n")
            file.write("DATUM Unknown\n")
            file.write("AXIS_NAME X Y Z\n")
            file.write("AXIS_UNIT m m m\n")
            file.write("ZPOSITIVE Elevation\n")
            file.write("END_ORIGINAL_COORDINATE_SYSTEM\n")
            file.write("TFACE\n")
            
            # 写入顶点
            for i, v in enumerate(vertices):
                file.write(f"VRTX {i+1} {v[0]:.9f} {v[1]:.9f} {v[2]:.9f} \n")
            
            # 写入面
            for face in faces:
                file.write(f"TRGL {face[0]+1} {face[1]+1} {face[2]+1}\n")
            
            file.write("END\n")
        
        print(f"✓ 成功写入TS文件: {len(vertices)} 顶点, {len(faces)} 面")
        return True
        
    except Exception as e:
        print(f"✗ 写入TS文件失败: {e}")
        return False

def main():
    """主处理函数"""
    print("=" * 60)
    print("q_1208.ts 有效闭合脚本 v3.0")
    print("在保持主要形态特征的前提下实现有效闭合")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查输入文件
    input_file = "q_1208.ts"
    if not os.path.exists(input_file):
        print(f"✗ 输入文件 {input_file} 不存在")
        return False
    
    start_time = time.time()
    
    # 步骤1: 读取文件
    print(f"\n步骤1: 读取TS文件...")
    vertices, faces = read_ts_file(input_file)
    
    if len(vertices) == 0 or len(faces) == 0:
        print("✗ 无法读取输入文件")
        return False
    
    # 步骤2: 有效闭合
    print(f"\n步骤2: 有效闭合处理...")
    final_mesh = effective_closure(vertices, faces)
    
    # 步骤3: 保存结果
    print(f"\n步骤3: 保存结果...")
    final_vertices, final_faces = extract_vertices_faces(final_mesh)
    
    output_file = "q_1208_effective.ts"
    write_success = write_ts_file(output_file, final_vertices, final_faces, "q_1208_effective")
    
    if not write_success:
        return False
    
    # 统计信息
    end_time = time.time()
    print(f"\n" + "=" * 60)
    print("处理完成:")
    print(f"原始: {len(vertices)} 顶点, {len(faces)} 面")
    print(f"最终: {len(final_vertices)} 顶点, {len(final_faces)} 面")
    
    vertex_change = abs(len(final_vertices) - len(vertices)) / len(vertices) * 100
    face_change = abs(len(final_faces) - len(faces)) / len(faces) * 100
    
    print(f"变化: 顶点 {vertex_change:.1f}%, 面 {face_change:.1f}%")
    print(f"处理时间: {end_time - start_time:.2f} 秒")
    print(f"输出文件: {output_file}")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✓ 有效闭合脚本执行成功!")
            sys.exit(0)
        else:
            print("\n✗ 有效闭合脚本执行失败!")
            sys.exit(1)
    except Exception as e:
        print(f"\n未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
