#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
q_1208.ts 地质结构闭合脚本
专门针对复杂地质结构的空洞填补
基于图片观察，这是一个有大量空洞的复杂地质体
"""

import numpy as np
import os
import sys
import time
from datetime import datetime

try:
    import pyvista as pv
    print("✓ PyVista 可用")
except ImportError:
    print("✗ PyVista 不可用，请安装: pip install pyvista")
    sys.exit(1)

def read_ts_file(filename):
    """读取TS文件"""
    print(f"正在读取文件: {filename}")
    
    vertices = []
    faces = []
    vertex_map = {}
    current_idx = 0
    
    encodings = ['utf-8', 'gbk', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            with open(filename, 'r', encoding=encoding) as file:
                lines = file.readlines()
            
            for i, line in enumerate(lines):
                line = line.strip()
                
                if line.startswith('VRTX'):
                    parts = line.split()
                    if len(parts) >= 5:
                        try:
                            vrtx_id = int(parts[1])
                            x, y, z = float(parts[2]), float(parts[3]), float(parts[4])
                            vertices.append([x, y, z])
                            vertex_map[vrtx_id] = current_idx
                            current_idx += 1
                        except (ValueError, IndexError):
                            continue
                
                elif line.startswith('TRGL'):
                    parts = line.split()
                    if len(parts) >= 4:
                        try:
                            v1, v2, v3 = int(parts[1]), int(parts[2]), int(parts[3])
                            if v1 in vertex_map and v2 in vertex_map and v3 in vertex_map:
                                faces.append([vertex_map[v1], vertex_map[v2], vertex_map[v3]])
                        except (ValueError, IndexError):
                            continue
                
                if (i + 1) % 20000 == 0:
                    print(f"已处理 {i + 1} 行...")
            
            vertices = np.array(vertices, dtype=np.float64)
            faces = np.array(faces, dtype=np.int32)
            
            print(f"✓ 成功读取: {len(vertices)} 个顶点, {len(faces)} 个面")
            return vertices, faces
            
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"读取失败 ({encoding}): {e}")
            continue
    
    return np.array([]), np.array([])

def check_closure(mesh):
    """检查网格闭合性"""
    try:
        boundaries = mesh.extract_feature_edges(boundary_edges=True, 
                                               non_manifold_edges=False, 
                                               manifold_edges=False)
        return boundaries.n_cells
    except:
        return -1

def geological_closure(vertices, faces):
    """地质结构闭合 - 专门处理复杂地质体的空洞"""
    print("\n=== 地质结构闭合 ===")
    print("针对复杂地质结构的空洞填补")
    
    # 创建PyVista网格
    faces_with_header = []
    for face in faces:
        faces_with_header.extend([3, face[0], face[1], face[2]])
    
    mesh = pv.PolyData(vertices, faces_with_header)
    print(f"原始网格: {mesh.n_points} 顶点, {mesh.n_cells} 面")
    
    # 记录原始信息
    original_bounds = mesh.bounds
    print(f"坐标范围:")
    print(f"  X: [{original_bounds[0]:.2f}, {original_bounds[1]:.2f}]")
    print(f"  Y: [{original_bounds[2]:.2f}, {original_bounds[3]:.2f}]")
    print(f"  Z: [{original_bounds[4]:.2f}, {original_bounds[5]:.2f}]")
    
    # 检查初始状态
    initial_boundaries = check_closure(mesh)
    print(f"初始边界边数: {initial_boundaries}")
    
    # 分析连通分量
    try:
        connected = mesh.connectivity()
        n_components = len(np.unique(connected['RegionId']))
        print(f"连通分量数量: {n_components}")
    except:
        n_components = "未知"
        print(f"连通分量数量: {n_components}")
    
    # 策略1: 渐进式大孔洞填充
    print(f"\n策略1: 渐进式大孔洞填充")
    current_mesh = mesh
    
    # 针对地质结构的大孔洞填充
    hole_sizes = [50, 100, 200, 500, 1000, 2000, 5000, 10000, 20000]
    
    for hole_size in hole_sizes:
        try:
            print(f"  尝试孔洞大小: {hole_size}")
            filled_mesh = current_mesh.fill_holes(hole_size=hole_size)
            
            boundaries = check_closure(filled_mesh)
            improvement = initial_boundaries - boundaries if initial_boundaries > 0 else 0
            improvement_rate = improvement / initial_boundaries * 100 if initial_boundaries > 0 else 0
            
            print(f"    边界边: {boundaries} (改善 {improvement_rate:.1f}%)")
            print(f"    面数: {filled_mesh.n_cells}")
            
            # 检查是否有显著改善
            if boundaries < initial_boundaries * 0.9:  # 改善10%以上
                current_mesh = filled_mesh
                print(f"    ✓ 接受改善")
                
                # 如果改善很大，继续
                if boundaries < initial_boundaries * 0.5:
                    print(f"    🎉 显著改善，继续填充")
                    continue
            else:
                print(f"    - 改善有限")
                
        except Exception as e:
            print(f"    填充失败: {e}")
            continue
    
    # 策略2: 如果还有很多空洞，尝试体素化重建
    current_boundaries = check_closure(current_mesh)
    print(f"\n当前边界边数: {current_boundaries}")
    
    if current_boundaries > 1000:
        print(f"\n策略2: 体素化重建")
        try:
            # 体素化
            voxel_size = (original_bounds[1] - original_bounds[0]) / 100  # 自适应体素大小
            print(f"  体素大小: {voxel_size:.2f}")
            
            voxelized = current_mesh.voxelize(density=voxel_size)
            if voxelized.n_cells > 0:
                # 提取表面
                surface = voxelized.extract_surface()
                surface = surface.triangulate()
                
                boundaries = check_closure(surface)
                print(f"  体素化结果: {boundaries} 边界边, {surface.n_cells} 面")
                
                # 检查坐标范围是否合理
                new_bounds = surface.bounds
                bounds_ok = True
                for i in range(6):
                    if abs(new_bounds[i] - original_bounds[i]) > abs(original_bounds[i]) * 0.2:
                        bounds_ok = False
                        break
                
                if bounds_ok and boundaries < current_boundaries:
                    current_mesh = surface
                    print(f"  ✓ 接受体素化结果")
                else:
                    print(f"  ✗ 体素化效果不佳")
                    
        except Exception as e:
            print(f"  体素化失败: {e}")
    
    # 策略3: 最后的凸包尝试（如果其他方法都不够好）
    final_boundaries = check_closure(current_mesh)
    
    if final_boundaries > 2000:
        print(f"\n策略3: 凸包重建（最后手段）")
        try:
            # 创建凸包
            points = pv.PolyData(mesh.points)
            hull = points.convex_hull()
            
            hull_boundaries = check_closure(hull)
            print(f"  凸包结果: {hull_boundaries} 边界边, {hull.n_cells} 面")
            
            if hull_boundaries == 0:
                print(f"  ✓ 凸包实现完全闭合")
                
                # 检查体积变化是否合理
                try:
                    original_volume = mesh.volume if hasattr(mesh, 'volume') else 0
                    hull_volume = hull.volume if hasattr(hull, 'volume') else 0
                    
                    if original_volume > 0:
                        volume_ratio = hull_volume / original_volume
                        print(f"  体积比: {volume_ratio:.2f}")
                        
                        if volume_ratio < 5:  # 体积增加不超过5倍
                            current_mesh = hull
                            print(f"  ✓ 接受凸包结果")
                        else:
                            print(f"  ✗ 凸包体积变化过大")
                    else:
                        current_mesh = hull
                        print(f"  ✓ 接受凸包结果")
                        
                except:
                    current_mesh = hull
                    print(f"  ✓ 接受凸包结果")
                    
        except Exception as e:
            print(f"  凸包创建失败: {e}")
    
    # 最终统计
    final_boundaries = check_closure(current_mesh)
    final_vertex_change = abs(current_mesh.n_points - mesh.n_points) / mesh.n_points * 100
    final_face_change = abs(current_mesh.n_cells - mesh.n_cells) / mesh.n_cells * 100
    total_improvement = (initial_boundaries - final_boundaries) / initial_boundaries * 100 if initial_boundaries > 0 else 0
    
    print(f"\n" + "="*60)
    print(f"地质结构闭合最终结果:")
    print(f"  顶点: {mesh.n_points} → {current_mesh.n_points} (变化 {final_vertex_change:.1f}%)")
    print(f"  面数: {mesh.n_cells} → {current_mesh.n_cells} (变化 {final_face_change:.1f}%)")
    print(f"  边界边: {initial_boundaries} → {final_boundaries} (改善 {total_improvement:.1f}%)")
    
    # 评估结果
    if final_boundaries == 0:
        print(f"🎉 完全闭合成功！")
    elif final_boundaries < 100:
        print(f"✓ 优秀闭合！边界边已减少到 {final_boundaries}")
    elif final_boundaries < 500:
        print(f"✓ 良好闭合！边界边已减少到 {final_boundaries}")
    elif total_improvement > 50:
        print(f"✓ 显著改善！边界边减少了 {total_improvement:.1f}%")
    else:
        print(f"⚠ 这是一个非常复杂的地质结构，可能需要手动处理")
    
    print("="*60)
    
    return current_mesh

def extract_vertices_faces(mesh):
    """从PyVista网格提取顶点和面"""
    vertices = mesh.points
    faces = []
    
    for i in range(mesh.n_cells):
        cell = mesh.get_cell(i)
        if cell.n_points == 3:
            faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])
    
    return vertices, np.array(faces)

def write_ts_file(filename, vertices, faces, name=None):
    """写入TS文件"""
    if name is None:
        name = os.path.splitext(os.path.basename(filename))[0]
    
    print(f"正在写入TS文件: {filename}")
    
    try:
        with open(filename, "w", encoding='utf-8') as file:
            file.write("GOCAD TSurf 1 \n")
            file.write("HEADER {\n")
            file.write(f"name: {name}\n")
            file.write("*solid*color: #f0e68c\n")
            file.write("use_feature_color: false\n")
            file.write("}\n")
            file.write("GOCAD_ORIGINAL_COORDINATE_SYSTEM\n")
            file.write("NAME \"SKUA Local\"\n")
            file.write("PROJECTION Unknown\n")
            file.write("DATUM Unknown\n")
            file.write("AXIS_NAME X Y Z\n")
            file.write("AXIS_UNIT m m m\n")
            file.write("ZPOSITIVE Elevation\n")
            file.write("END_ORIGINAL_COORDINATE_SYSTEM\n")
            file.write("TFACE\n")
            
            # 写入顶点
            for i, v in enumerate(vertices):
                file.write(f"VRTX {i+1} {v[0]:.9f} {v[1]:.9f} {v[2]:.9f} \n")
            
            # 写入面
            for face in faces:
                file.write(f"TRGL {face[0]+1} {face[1]+1} {face[2]+1}\n")
            
            file.write("END\n")
        
        print(f"✓ 成功写入TS文件: {len(vertices)} 顶点, {len(faces)} 面")
        return True
        
    except Exception as e:
        print(f"✗ 写入TS文件失败: {e}")
        return False

def main():
    """主处理函数"""
    print("=" * 60)
    print("q_1208.ts 地质结构闭合脚本")
    print("专门针对复杂地质结构的空洞填补")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查输入文件
    input_file = "q_1208.ts"
    if not os.path.exists(input_file):
        print(f"✗ 输入文件 {input_file} 不存在")
        return False
    
    start_time = time.time()
    
    # 步骤1: 读取文件
    print(f"\n步骤1: 读取TS文件...")
    vertices, faces = read_ts_file(input_file)
    
    if len(vertices) == 0 or len(faces) == 0:
        print("✗ 无法读取输入文件")
        return False
    
    # 步骤2: 地质结构闭合
    print(f"\n步骤2: 地质结构闭合处理...")
    final_mesh = geological_closure(vertices, faces)
    
    # 步骤3: 保存结果
    print(f"\n步骤3: 保存结果...")
    final_vertices, final_faces = extract_vertices_faces(final_mesh)
    
    output_file = "q_1208_geological.ts"
    write_success = write_ts_file(output_file, final_vertices, final_faces, "q_1208_geological")
    
    if not write_success:
        return False
    
    # 统计信息
    end_time = time.time()
    print(f"\n" + "=" * 60)
    print("处理完成:")
    print(f"原始: {len(vertices)} 顶点, {len(faces)} 面")
    print(f"最终: {len(final_vertices)} 顶点, {len(final_faces)} 面")
    
    vertex_change = abs(len(final_vertices) - len(vertices)) / len(vertices) * 100
    face_change = abs(len(final_faces) - len(faces)) / len(faces) * 100
    
    print(f"变化: 顶点 {vertex_change:.1f}%, 面 {face_change:.1f}%")
    print(f"处理时间: {end_time - start_time:.2f} 秒")
    print(f"输出文件: {output_file}")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✓ 地质结构闭合脚本执行成功!")
            sys.exit(0)
        else:
            print("\n✗ 地质结构闭合脚本执行失败!")
            sys.exit(1)
    except Exception as e:
        print(f"\n未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
