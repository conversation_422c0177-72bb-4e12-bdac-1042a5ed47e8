#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
q_1208.ts文件高级形态保持闭合脚本
使用更强力的方法在保持形态的前提下闭合复杂表面
"""

import numpy as np
import os
import sys
import time
from datetime import datetime
from collections import defaultdict

# 尝试导入必要的库
try:
    import pyvista as pv
    PYVISTA_AVAILABLE = True
    print("✓ PyVista 可用")
except ImportError:
    PYVISTA_AVAILABLE = False
    print("✗ PyVista 不可用")
    sys.exit(1)

try:
    import tetgen
    TETGEN_AVAILABLE = True
    print("✓ TetGen 可用")
except ImportError:
    TETGEN_AVAILABLE = False
    print("✗ TetGen 不可用")
    sys.exit(1)

def read_tsurf_data(file_path):
    """读取TS文件数据"""
    print(f"正在读取文件: {file_path}")
    
    encodings = ['utf-8', 'gbk', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            vrtx = []
            vrtx_map = {}
            trgl = []
            current_idx = 0
            
            with open(file_path, 'r', encoding=encoding) as file:
                lines = file.readlines()
                
            for i, line in enumerate(lines):
                line = line.strip()
                
                if line.startswith('VRTX'):
                    parts = line.split()
                    if len(parts) >= 5:
                        try:
                            vrtx_id = int(parts[1])
                            x = float(parts[2])
                            y = float(parts[3])
                            z = float(parts[4])
                            vrtx.append([x, y, z])
                            vrtx_map[vrtx_id] = current_idx
                            current_idx += 1
                        except (ValueError, IndexError):
                            continue
                
                elif line.startswith('TRGL'):
                    parts = line.split()
                    if len(parts) >= 4:
                        try:
                            v1 = int(parts[1])
                            v2 = int(parts[2])
                            v3 = int(parts[3])
                            
                            if v1 in vrtx_map and v2 in vrtx_map and v3 in vrtx_map:
                                trgl.append([vrtx_map[v1], vrtx_map[v2], vrtx_map[v3]])
                        except (ValueError, IndexError):
                            continue
                
                if (i + 1) % 10000 == 0:
                    print(f"已处理 {i + 1} 行...")
            
            vertices = np.array(vrtx, dtype=np.float64)
            faces = np.array(trgl, dtype=np.int32)
            
            print(f"成功读取: {len(vertices)} 个顶点, {len(faces)} 个面")
            return vertices, faces
            
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"读取文件时出错: {e}")
            continue
    
    return np.array([]), np.array([])

def advanced_shape_preserving_closure(vertices, faces):
    """高级形态保持闭合方法"""
    print("\n=== 高级形态保持闭合 ===")
    
    try:
        # 创建网格
        faces_with_header = []
        for face in faces:
            faces_with_header.extend([3, face[0], face[1], face[2]])
        
        mesh = pv.PolyData(vertices, faces_with_header)
        print(f"原始网格: {mesh.n_points} 顶点, {mesh.n_cells} 面")
        
        # 方法1: 超大孔洞填充
        print("方法1: 超大孔洞填充...")
        result = super_large_hole_filling(mesh)
        if result is not None:
            return result
        
        # 方法2: 边界包围盒闭合
        print("方法2: 边界包围盒闭合...")
        result = bounding_box_closure(mesh)
        if result is not None:
            return result
        
        # 方法3: 分层闭合
        print("方法3: 分层闭合...")
        result = layered_closure(mesh)
        if result is not None:
            return result
        
        # 方法4: 强制闭合（最后手段）
        print("方法4: 强制闭合...")
        result = force_closure(mesh)
        if result is not None:
            return result
        
        print("所有高级闭合方法都失败了")
        return None, None
        
    except Exception as e:
        print(f"高级闭合失败: {e}")
        return None, None

def super_large_hole_filling(mesh):
    """超大孔洞填充"""
    try:
        print("  执行超大孔洞填充...")
        
        # 尝试非常大的孔洞大小
        hole_sizes = [1000, 2000, 5000, 10000, 20000, 50000, 100000]
        
        for hole_size in hole_sizes:
            try:
                print(f"    尝试孔洞大小: {hole_size}")
                filled_mesh = mesh.fill_holes(hole_size=hole_size)
                
                # 检查闭合性
                boundaries = filled_mesh.extract_feature_edges(boundary_edges=True, 
                                                              non_manifold_edges=False, 
                                                              manifold_edges=False)
                
                print(f"      结果: {boundaries.n_cells} 边界边")
                
                if boundaries.n_cells == 0:
                    print(f"  ✓ 超大孔洞填充成功 (孔洞大小: {hole_size})")
                    return extract_vertices_faces(filled_mesh)
                elif boundaries.n_cells < 100:  # 接受少量边界边
                    print(f"  ⚠ 基本闭合 (剩余 {boundaries.n_cells} 边界边)")
                    return extract_vertices_faces(filled_mesh)
                    
            except Exception as e:
                print(f"      孔洞大小 {hole_size} 失败: {e}")
                continue
        
        return None
        
    except Exception as e:
        print(f"  超大孔洞填充失败: {e}")
        return None

def bounding_box_closure(mesh):
    """边界包围盒闭合"""
    try:
        print("  执行边界包围盒闭合...")
        
        # 获取边界点
        boundaries = mesh.extract_feature_edges(boundary_edges=True, 
                                               non_manifold_edges=False, 
                                               manifold_edges=False)
        
        if boundaries.n_cells == 0:
            return extract_vertices_faces(mesh)
        
        boundary_points = boundaries.points
        
        # 计算边界包围盒
        bbox_min = np.min(boundary_points, axis=0)
        bbox_max = np.max(boundary_points, axis=0)
        bbox_center = (bbox_min + bbox_max) / 2
        
        print(f"    边界包围盒中心: {bbox_center}")
        
        # 创建包围盒的8个顶点
        bbox_vertices = np.array([
            [bbox_min[0], bbox_min[1], bbox_min[2]],
            [bbox_max[0], bbox_min[1], bbox_min[2]],
            [bbox_max[0], bbox_max[1], bbox_min[2]],
            [bbox_min[0], bbox_max[1], bbox_min[2]],
            [bbox_min[0], bbox_min[1], bbox_max[2]],
            [bbox_max[0], bbox_min[1], bbox_max[2]],
            [bbox_max[0], bbox_max[1], bbox_max[2]],
            [bbox_min[0], bbox_max[1], bbox_max[2]]
        ])
        
        # 创建包围盒面
        bbox_faces = np.array([
            [0, 1, 2], [0, 2, 3],  # 底面
            [4, 7, 6], [4, 6, 5],  # 顶面
            [0, 4, 5], [0, 5, 1],  # 前面
            [2, 6, 7], [2, 7, 3],  # 后面
            [0, 3, 7], [0, 7, 4],  # 左面
            [1, 5, 6], [1, 6, 2]   # 右面
        ])
        
        # 合并原始网格和包围盒
        all_vertices = np.vstack([mesh.points, bbox_vertices])
        
        # 创建合并的面列表
        all_faces = []
        
        # 添加原始面
        for i in range(mesh.n_cells):
            cell = mesh.get_cell(i)
            if cell.n_points == 3:
                all_faces.extend([3, cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])
        
        # 添加包围盒面（调整索引）
        bbox_offset = mesh.n_points
        for face in bbox_faces:
            all_faces.extend([3, face[0] + bbox_offset, face[1] + bbox_offset, face[2] + bbox_offset])
        
        # 创建合并网格
        combined_mesh = pv.PolyData(all_vertices, all_faces)
        
        # 检查结果
        boundaries_check = combined_mesh.extract_feature_edges(boundary_edges=True, 
                                                              non_manifold_edges=False, 
                                                              manifold_edges=False)
        
        print(f"    包围盒闭合后边界边: {boundaries_check.n_cells}")
        
        if boundaries_check.n_cells == 0:
            print("  ✓ 边界包围盒闭合成功")
            return extract_vertices_faces(combined_mesh)
        else:
            print("  ✗ 边界包围盒闭合失败")
            return None
        
    except Exception as e:
        print(f"  边界包围盒闭合失败: {e}")
        return None

def layered_closure(mesh):
    """分层闭合方法"""
    try:
        print("  执行分层闭合...")
        
        current_mesh = mesh
        
        # 多层处理
        for layer in range(5):
            print(f"    处理层 {layer + 1}...")
            
            # 检查当前边界
            boundaries = current_mesh.extract_feature_edges(boundary_edges=True, 
                                                           non_manifold_edges=False, 
                                                           manifold_edges=False)
            
            print(f"      当前边界边: {boundaries.n_cells}")
            
            if boundaries.n_cells == 0:
                print(f"  ✓ 分层闭合在第 {layer + 1} 层成功")
                return extract_vertices_faces(current_mesh)
            
            # 这一层的处理
            layer_hole_size = 1000 * (2 ** layer)  # 指数增长
            try:
                current_mesh = current_mesh.fill_holes(hole_size=layer_hole_size)
                current_mesh = current_mesh.clean(tolerance=1e-8)
            except Exception as e:
                print(f"      层 {layer + 1} 处理失败: {e}")
                break
        
        # 最终检查
        boundaries = current_mesh.extract_feature_edges(boundary_edges=True, 
                                                       non_manifold_edges=False, 
                                                       manifold_edges=False)
        
        if boundaries.n_cells < 1000:  # 接受少量边界边
            print(f"  ⚠ 分层闭合基本成功 (剩余 {boundaries.n_cells} 边界边)")
            return extract_vertices_faces(current_mesh)
        else:
            print("  ✗ 分层闭合失败")
            return None
        
    except Exception as e:
        print(f"  分层闭合失败: {e}")
        return None

def force_closure(mesh):
    """强制闭合（最后手段）"""
    try:
        print("  执行强制闭合...")
        
        # 获取所有边界点
        boundaries = mesh.extract_feature_edges(boundary_edges=True, 
                                               non_manifold_edges=False, 
                                               manifold_edges=False)
        
        if boundaries.n_cells == 0:
            return extract_vertices_faces(mesh)
        
        boundary_points = boundaries.points
        
        # 计算边界点的凸包
        boundary_cloud = pv.PolyData(boundary_points)
        boundary_hull = boundary_cloud.delaunay_3d().extract_surface()
        
        print(f"    边界凸包: {boundary_hull.n_points} 顶点, {boundary_hull.n_cells} 面")
        
        # 合并原始网格和边界凸包
        # 获取原始面
        original_faces = []
        for i in range(mesh.n_cells):
            cell = mesh.get_cell(i)
            if cell.n_points == 3:
                original_faces.extend([3, cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])
        
        # 获取凸包面
        hull_faces = []
        hull_offset = mesh.n_points
        for i in range(boundary_hull.n_cells):
            cell = boundary_hull.get_cell(i)
            if cell.n_points == 3:
                hull_faces.extend([3, 
                                  cell.point_ids[0] + hull_offset, 
                                  cell.point_ids[1] + hull_offset, 
                                  cell.point_ids[2] + hull_offset])
        
        # 合并顶点和面
        all_vertices = np.vstack([mesh.points, boundary_hull.points])
        all_faces = original_faces + hull_faces
        
        # 创建强制闭合网格
        forced_mesh = pv.PolyData(all_vertices, all_faces)
        
        # 清理和验证
        forced_mesh = forced_mesh.clean(tolerance=1e-8)
        
        boundaries_check = forced_mesh.extract_feature_edges(boundary_edges=True, 
                                                            non_manifold_edges=False, 
                                                            manifold_edges=False)
        
        print(f"    强制闭合后边界边: {boundaries_check.n_cells}")
        
        if boundaries_check.n_cells < 100:  # 接受少量边界边
            print("  ✓ 强制闭合成功")
            return extract_vertices_faces(forced_mesh)
        else:
            print("  ✗ 强制闭合失败")
            return None
        
    except Exception as e:
        print(f"  强制闭合失败: {e}")
        return None

def extract_vertices_faces(mesh):
    """从PyVista网格提取顶点和面"""
    vertices = mesh.points
    faces = []
    
    for i in range(mesh.n_cells):
        cell = mesh.get_cell(i)
        if cell.n_points == 3:
            faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])
    
    faces = np.array(faces)
    return vertices, faces

def verify_closure(vertices, faces):
    """验证闭合性"""
    print("\n=== 验证闭合性 ===")
    
    try:
        faces_with_header = []
        for face in faces:
            faces_with_header.extend([3, face[0], face[1], face[2]])
        
        mesh = pv.PolyData(vertices, faces_with_header)
        
        boundaries = mesh.extract_feature_edges(boundary_edges=True, 
                                               non_manifold_edges=False, 
                                               manifold_edges=False)
        
        non_manifold = mesh.extract_feature_edges(boundary_edges=False, 
                                                 non_manifold_edges=True, 
                                                 manifold_edges=False)
        
        print(f"边界边数: {boundaries.n_cells}")
        print(f"非流形边数: {non_manifold.n_cells}")
        
        if boundaries.n_cells == 0 and non_manifold.n_cells == 0:
            print("✓ 表面完全闭合且为流形")
            return True
        elif boundaries.n_cells < 100:
            print("⚠ 表面基本闭合（少量边界边）")
            return True
        else:
            print("✗ 表面仍有较多问题")
            return False
        
    except Exception as e:
        print(f"验证失败: {e}")
        return False

def tetgen_pY_only(vertices, faces):
    """使用TetGen 'pY'参数进行四面体化"""
    print("\n=== TetGen 'pY'参数四面体化 ===")
    
    try:
        print("创建TetGen对象...")
        tet = tetgen.TetGen(vertices, faces)
        
        print("执行'pY'参数四面体化...")
        tet.tetrahedralize('pY')
        
        tetrahedral_mesh = tet.grid
        
        if tetrahedral_mesh.n_cells > 0:
            print(f"✓ 'pY'参数四面体化成功!")
            print(f"  结果: {tetrahedral_mesh.n_points} 顶点, {tetrahedral_mesh.n_cells} 四面体")
            return tetrahedral_mesh
        else:
            print("✗ 'pY'参数四面体化结果为空")
            return None
            
    except Exception as e:
        print(f"✗ 'pY'参数四面体化失败: {e}")
        return None

def write_ts_file(file_path, vertices, faces, name=None):
    """写入TS文件"""
    try:
        if name is None:
            name = os.path.splitext(os.path.basename(file_path))[0]

        print(f"正在写入TS文件: {file_path}")

        with open(file_path, "w", encoding='utf-8') as file:
            file.write("GOCAD TSurf 1 \n")
            file.write("HEADER {\n")
            file.write(f"name: {name}\n")
            file.write("*solid*color: #f0e68c\n")
            file.write("}\n")
            file.write("TFACE\n")

            for i, v in enumerate(vertices):
                file.write(f"VRTX {i+1} {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")

            for face in faces:
                file.write(f"TRGL {face[0]+1} {face[1]+1} {face[2]+1}\n")

            file.write("END\n")

        print(f"✓ 成功写入TS文件")
        return True
    except Exception as e:
        print(f"写入TS文件失败: {e}")
        return False

def main():
    """主处理函数"""
    print("=" * 60)
    print("q_1208.ts 高级形态保持闭合脚本")
    print("使用更强力的方法闭合复杂表面")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 检查输入文件
    input_file = "q_1208.ts"
    if not os.path.exists(input_file):
        print(f"错误: 输入文件 {input_file} 不存在")
        return False

    # 创建输出目录
    output_dir = "advanced_closure_output"
    os.makedirs(output_dir, exist_ok=True)
    print(f"输出目录: {output_dir}")

    start_time = time.time()

    # 步骤1: 读取文件
    print(f"\n步骤1: 读取TS文件...")
    vertices, faces = read_tsurf_data(input_file)

    if len(vertices) == 0 or len(faces) == 0:
        print("错误: 无法读取输入文件")
        return False

    print(f"✓ 读取完成: {len(vertices)} 顶点, {len(faces)} 面")

    # 步骤2: 高级形态保持闭合
    print(f"\n步骤2: 高级形态保持闭合...")
    closed_vertices, closed_faces = advanced_shape_preserving_closure(vertices, faces)

    if closed_vertices is None or closed_faces is None:
        print("错误: 高级闭合失败")
        return False

    print(f"✓ 闭合完成: {len(closed_vertices)} 顶点, {len(closed_faces)} 面")

    # 步骤3: 验证闭合性
    print(f"\n步骤3: 验证闭合性...")
    is_closed = verify_closure(closed_vertices, closed_faces)

    # 保存闭合后的表面
    closed_ts_file = os.path.join(output_dir, "q_1208_advanced_closed.ts")
    write_ts_file(closed_ts_file, closed_vertices, closed_faces, "q_1208_advanced_closed")

    # 步骤4: TetGen 'pY'四面体化
    print(f"\n步骤4: TetGen 'pY'四面体化...")
    tet_mesh = tetgen_pY_only(closed_vertices, closed_faces)

    if tet_mesh is None:
        print("错误: 'pY'参数四面体化失败")
        return False

    # 步骤5: 提取表面并保存
    print(f"\n步骤5: 提取表面并保存...")

    # 提取表面
    surface = tet_mesh.extract_surface()
    surface = surface.triangulate()

    # 获取表面数据
    surface_vertices = surface.points
    surface_faces = []
    for i in range(surface.n_cells):
        cell = surface.get_cell(i)
        if cell.n_points == 3:
            surface_faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])

    surface_faces = np.array(surface_faces)

    # 保存最终结果
    output_ts_file = os.path.join(output_dir, "q_1208_advanced_final.ts")
    success = write_ts_file(output_ts_file, surface_vertices, surface_faces, "q_1208_advanced_final")

    # 保存VTK文件
    surface.save(os.path.join(output_dir, "q_1208_advanced_surface.vtk"))
    tet_mesh.save(os.path.join(output_dir, "q_1208_advanced_volume.vtk"))

    # 统计信息
    end_time = time.time()
    print(f"\n" + "=" * 60)
    print("高级形态保持闭合和TetGen 'pY'参数四面体化完成:")
    print(f"原始网格: {len(vertices)} 顶点, {len(faces)} 面")
    print(f"闭合后: {len(closed_vertices)} 顶点, {len(closed_faces)} 面")
    print(f"四面体网格: {tet_mesh.n_points} 顶点, {tet_mesh.n_cells} 四面体")
    print(f"最终表面: {len(surface_vertices)} 顶点, {len(surface_faces)} 面")
    print(f"处理时间: {end_time - start_time:.2f} 秒")
    print(f"闭合表面文件: {closed_ts_file}")
    print(f"最终结果文件: {output_ts_file}")

    if is_closed:
        print("✓ 成功使用高级方法闭合表面并完成四面体化!")
    else:
        print("⚠ 完成四面体化但表面可能仍有少量问题")

    print("=" * 60)

    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✓ 高级闭合脚本执行成功!")
            sys.exit(0)
        else:
            print("\n✗ 高级闭合脚本执行失败!")
            sys.exit(1)
    except Exception as e:
        print(f"\n未预期的错误: {e}")
        sys.exit(1)
